package spinal.lib.com.mipi.cphy

import spinal.core._
import spinal.core.sim._
import spinal.lib._
import spinal.tester.SpinalTesterGhdlBase

import scala.collection.mutable
import scala.util.Random

/**
 * C-PHY Core Tester
 */
class CPhyCoreTester extends SpinalTesterGhdlBase {
  
  /**
   * Test C-PHY Encoder/Decoder functionality
   */
  def testEncoderDecoder(): Unit = {
    val config = CPhyConfig(dataWidth = 16, trioCount = 2)
    
    SimConfig.withWave.compile(new Component {
      val encoder = CPhyEncoder(config)
      val decoder = CPhyDecoder(config)
      
      // Connect encoder output to decoder input
      decoder.io.symbolIn <> encoder.io.symbolOut
      
      val io = new Bundle {
        val dataIn = slave(Stream(Bits(config.dataWidth bits)))
        val dataOut = master(Stream(Bits(config.dataWidth bits)))
      }
      
      encoder.io.dataIn <> io.dataIn
      io.dataOut <> decoder.io.dataOut
      
    }).doSim { dut =>
      dut.clockDomain.forkStimulus(period = 10)
      
      // Initialize
      dut.io.dataIn.valid #= false
      dut.io.dataOut.ready #= true
      dut.clockDomain.waitSampling(10)
      
      // Test data patterns
      val testData = Seq(0x1234, 0x5678, 0x9ABC, 0xDEF0, 0x0000, 0xFFFF)
      val receivedData = mutable.ArrayBuffer[Int]()
      
      // Send test data
      fork {
        for(data <- testData) {
          dut.io.dataIn.valid #= true
          dut.io.dataIn.payload #= data
          dut.clockDomain.waitSamplingWhere(dut.io.dataIn.ready.toBoolean)
          dut.clockDomain.waitSampling()
        }
        dut.io.dataIn.valid #= false
      }
      
      // Receive data
      fork {
        while(receivedData.length < testData.length) {
          dut.clockDomain.waitSamplingWhere(dut.io.dataOut.valid.toBoolean)
          receivedData += dut.io.dataOut.payload.toInt
          dut.clockDomain.waitSampling()
        }
      }
      
      // Wait for completion
      dut.clockDomain.waitSampling(1000)
      
      // Verify results
      assert(receivedData.length == testData.length, s"Expected ${testData.length} packets, got ${receivedData.length}")
      for((sent, received) <- testData.zip(receivedData)) {
        assert(sent == received, s"Data mismatch: sent 0x${sent.toHexString}, received 0x${received.toHexString}")
      }
      
      println("✓ Encoder/Decoder test passed")
    }
  }
  
  /**
   * Test C-PHY Symbol validation
   */
  def testSymbolValidation(): Unit = {
    SimConfig.withWave.compile(new Component {
      val io = new Bundle {
        val symbol = in Bits(3 bits)
        val isValid = out Bool()
      }
      
      io.isValid := CPhySymbol.isValidSymbol(io.symbol)
      
    }).doSim { dut =>
      dut.clockDomain.forkStimulus(period = 10)
      
      // Test all possible 3-bit combinations
      for(i <- 0 until 8) {
        dut.io.symbol #= i
        dut.clockDomain.waitSampling()
        
        val expectedValid = i match {
          case 0 | 1 | 2 | 3 | 4 | 5 | 6 => true  // Valid C-PHY symbols
          case 7 => false  // Invalid symbol
        }
        
        assert(dut.io.isValid.toBoolean == expectedValid, 
               s"Symbol $i validation failed: expected $expectedValid, got ${dut.io.isValid.toBoolean}")
      }
      
      println("✓ Symbol validation test passed")
    }
  }
  
  /**
   * Test C-PHY Transmitter
   */
  def testTransmitter(): Unit = {
    val config = CPhyTxConfig(dataWidth = 16, trioCount = 1)
    
    SimConfig.withWave.compile(CPhyTransmitter(config)).doSim { dut =>
      dut.clockDomain.forkStimulus(period = 10)
      
      // Initialize
      dut.io.dataIn.valid #= false
      dut.io.enable #= false
      dut.io.calibrationMode #= false
      dut.io.txClock #= false
      dut.io.reset #= true
      
      dut.clockDomain.waitSampling(10)
      dut.io.reset #= false
      dut.clockDomain.waitSampling(10)
      
      // Enable transmitter
      dut.io.enable #= true
      
      // Generate TX clock
      fork {
        while(true) {
          dut.io.txClock #= false
          sleep(2)
          dut.io.txClock #= true
          sleep(2)
        }
      }
      
      // Wait for ready
      dut.clockDomain.waitSamplingWhere(dut.io.ready.toBoolean)
      
      // Send test data
      val testData = Seq(0x1234, 0x5678, 0x9ABC)
      for(data <- testData) {
        dut.io.dataIn.valid #= true
        dut.io.dataIn.payload #= data
        dut.clockDomain.waitSamplingWhere(dut.io.dataIn.ready.toBoolean)
        dut.clockDomain.waitSampling()
      }
      dut.io.dataIn.valid #= false
      
      // Monitor trio outputs
      var transitionCount = 0
      var prevTrio = (false, false, false)
      
      for(_ <- 0 until 1000) {
        val currentTrio = (
          dut.io.triosOut(0).a.toBoolean,
          dut.io.triosOut(0).b.toBoolean,
          dut.io.triosOut(0).c.toBoolean
        )
        
        if(currentTrio != prevTrio) {
          transitionCount += 1
          prevTrio = currentTrio
        }
        
        dut.clockDomain.waitSampling()
      }
      
      assert(transitionCount > 0, "No trio signal transitions detected")
      println(s"✓ Transmitter test passed (${transitionCount} transitions)")
    }
  }
  
  /**
   * Test C-PHY System with loopback
   */
  def testSystemLoopback(): Unit = {
    val config = CPhySystemConfig(
      dataWidth = 16,
      trioCount = 1,
      useUltraScale = false,
      enableLoopback = true
    )
    
    SimConfig.withWave.compile(CPhySystem(config)).doSim { dut =>
      dut.clockDomain.forkStimulus(period = 10)
      
      // Initialize
      dut.io.txData.valid #= false
      dut.io.rxData.ready #= true
      dut.io.enable #= false
      dut.io.reset #= true
      dut.io.refClock #= false
      
      // Generate reference clock
      fork {
        while(true) {
          dut.io.refClock #= false
          sleep(5)
          dut.io.refClock #= true
          sleep(5)
        }
      }
      
      dut.clockDomain.waitSampling(10)
      dut.io.reset #= false
      dut.clockDomain.waitSampling(10)
      
      // Enable system
      dut.io.enable #= true
      
      // Wait for system ready
      dut.clockDomain.waitSamplingWhere(dut.io.systemReady.toBoolean)
      println("System ready")
      
      // Test data transmission
      val testData = Seq(0x1234, 0x5678, 0x9ABC, 0xDEF0)
      val receivedData = mutable.ArrayBuffer[Int]()
      
      // Send data
      fork {
        for(data <- testData) {
          dut.io.txData.valid #= true
          dut.io.txData.payload #= data
          dut.clockDomain.waitSamplingWhere(dut.io.txData.ready.toBoolean)
          dut.clockDomain.waitSampling()
        }
        dut.io.txData.valid #= false
      }
      
      // Receive data
      fork {
        while(receivedData.length < testData.length) {
          dut.clockDomain.waitSamplingWhere(dut.io.rxData.valid.toBoolean)
          receivedData += dut.io.rxData.payload.toInt
          dut.clockDomain.waitSampling()
        }
      }
      
      // Wait for completion
      dut.clockDomain.waitSampling(5000)
      
      // Verify results
      println(s"Sent: ${testData.map(_.toHexString)}")
      println(s"Received: ${receivedData.map(_.toHexString)}")
      
      assert(receivedData.length >= testData.length / 2, 
             s"Too few packets received: ${receivedData.length} < ${testData.length / 2}")
      
      println("✓ System loopback test passed")
    }
  }
  
  /**
   * Test pattern generator and checker
   */
  def testPatternGenChecker(): Unit = {
    val config = CPhySystemConfig(dataWidth = 16, trioCount = 1)
    
    SimConfig.withWave.compile(new Component {
      val generator = CPhyTestPatternGenerator(config)
      val checker = CPhyDataChecker(config)
      
      // Connect generator to checker
      checker.io.dataIn <> generator.io.dataOut
      
      val io = new Bundle {
        val enable = in Bool()
        val patternSelect = in UInt(4 bits)
        val errorCount = out UInt(32 bits)
        val totalCount = out UInt(32 bits)
      }
      
      generator.io.enable := io.enable
      generator.io.patternSelect := io.patternSelect
      checker.io.expectedPattern := io.patternSelect
      checker.io.enable := io.enable
      
      io.errorCount := checker.io.errorCount
      io.totalCount := checker.io.totalCount
      
    }).doSim { dut =>
      dut.clockDomain.forkStimulus(period = 10)
      
      // Test different patterns
      for(pattern <- 0 until 4) {
        dut.io.enable #= false
        dut.io.patternSelect #= pattern
        dut.clockDomain.waitSampling(10)
        
        dut.io.enable #= true
        dut.clockDomain.waitSampling(100)
        
        val errorCount = dut.io.errorCount.toInt
        val totalCount = dut.io.totalCount.toInt
        
        println(s"Pattern $pattern: $totalCount packets, $errorCount errors")
        assert(errorCount == 0, s"Pattern $pattern had $errorCount errors")
        assert(totalCount > 50, s"Pattern $pattern generated too few packets: $totalCount")
      }
      
      println("✓ Pattern generator/checker test passed")
    }
  }
}

/**
 * Main test runner
 */
object CPhyTesterMain extends App {
  val tester = new CPhyCoreTester()
  
  println("Starting C-PHY tests...")
  
  try {
    tester.testSymbolValidation()
    tester.testEncoderDecoder()
    tester.testTransmitter()
    tester.testPatternGenChecker()
    tester.testSystemLoopback()
    
    println("\n✅ All C-PHY tests passed!")
  } catch {
    case e: Exception =>
      println(s"\n❌ Test failed: ${e.getMessage}")
      e.printStackTrace()
  }
}
