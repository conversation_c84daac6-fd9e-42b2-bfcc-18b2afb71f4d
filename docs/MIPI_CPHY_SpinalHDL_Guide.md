# MIPI C-PHY SpinalHDL Implementation Guide

## Overview

This guide provides comprehensive documentation for implementing MIPI C-PHY interfaces using SpinalHDL, specifically optimized for Xilinx UltraScale devices. The implementation supports the MIPI C-PHY v1.2 specification with 3-phase symbol encoding and trio differential signaling.

## Features

- **MIPI C-PHY v1.2 Compliance**: Full support for 3-phase symbol encoding
- **High Performance**: Up to 2.5 Gsps per trio (5.7 Gbps effective data rate)
- **Xilinx UltraScale Optimized**: Uses ISERDES3, OSERDES3, and MMCME4 primitives
- **Flexible Configuration**: Support for 1-4 trios with configurable data widths
- **Built-in Testing**: Comprehensive test patterns and error checking
- **Clock Recovery**: Advanced CDR for receiver synchronization

## Architecture

### Core Components

1. **CPhyEncoder/CPhyDecoder**: 16/7 symbol encoding/decoding
2. **CPhyTransmitter**: High-speed trio signal generation
3. **CPhyReceiver**: Trio signal reception and clock recovery
4. **CPhySystem**: Complete transceiver system

### Key Classes

```scala
// Basic configuration
case class CPhyConfig(
  dataWidth: Int = 16,      // Parallel data width
  trioCount: Int = 1,       // Number of C-PHY trios
  symbolRate: HertzNumber = 1 GHz,  // Symbol rate per trio
  enableTx: Boolean = true,
  enableRx: Boolean = true
)

// Trio interface (3-wire differential)
case class CPhyTrio() extends Bundle {
  val a = Bool()  // Wire A
  val b = Bool()  // Wire B  
  val c = Bool()  // Wire C
}
```

## Quick Start

### Basic Transmitter

```scala
import spinal.lib.com.mipi.cphy._

class MyCPhyTx extends Component {
  val io = new Bundle {
    val dataIn = slave(Stream(Bits(32 bits)))
    val triosOut = Vec(master(CPhyTrio()), 2)
    val refClock = in Bool()
    val enable = in Bool()
    val reset = in Bool()
    val ready = out Bool()
  }
  
  val config = CPhyTxConfig(
    dataWidth = 32,
    trioCount = 2,
    symbolRate = 2.28 GHz
  )
  
  val cphyTx = CPhyTxUltraScale(config)
  cphyTx.io.dataIn <> io.dataIn
  cphyTx.io.refClock := io.refClock
  cphyTx.io.enable := io.enable
  cphyTx.io.reset := io.reset
  
  for(i <- 0 until 2) {
    io.triosOut(i) <> cphyTx.io.triosOut(i)
  }
  
  io.ready := cphyTx.io.ready
}
```

### Basic Receiver

```scala
class MyCPhyRx extends Component {
  val io = new Bundle {
    val triosIn = Vec(slave(CPhyTrio()), 2)
    val dataOut = master(Stream(Bits(32 bits)))
    val refClock = in Bool()
    val enable = in Bool()
    val reset = in Bool()
    val locked = out Bool()
  }
  
  val config = CPhyRxConfig(
    dataWidth = 32,
    trioCount = 2,
    symbolRate = 2.28 GHz
  )
  
  val cphyRx = CPhyRxUltraScale(config)
  for(i <- 0 until 2) {
    cphyRx.io.triosIn(i) <> io.triosIn(i)
  }
  cphyRx.io.refClock := io.refClock
  cphyRx.io.enable := io.enable
  cphyRx.io.reset := io.reset
  
  io.dataOut <> cphyRx.io.dataOut
  io.locked := cphyRx.io.locked
}
```

## Configuration Options

### Transmitter Configuration

```scala
case class CPhyTxConfig(
  dataWidth: Int = 16,              // 8, 16, 32, 64 bits
  trioCount: Int = 1,               // 1-4 trios
  symbolRate: HertzNumber = 1 GHz,  // Up to 2.5 GHz
  enablePreEmphasis: Boolean = true,
  enableCalibration: Boolean = true
)
```

### Receiver Configuration

```scala
case class CPhyRxConfig(
  dataWidth: Int = 16,              // 8, 16, 32, 64 bits
  trioCount: Int = 1,               // 1-4 trios
  symbolRate: HertzNumber = 1 GHz,  // Up to 2.5 GHz
  enableCDR: Boolean = true,
  enableEqualizer: Boolean = true
)
```

## Performance Guidelines

### Symbol Rates and Data Rates

| Trios | Symbol Rate | Effective Data Rate | Use Case |
|-------|-------------|-------------------|----------|
| 1     | 1.0 Gsps    | 2.28 Gbps        | Low-speed cameras |
| 1     | 2.5 Gsps    | 5.7 Gbps         | High-speed cameras |
| 2     | 2.5 Gsps    | 11.4 Gbps        | 4K displays |
| 3     | 2.5 Gsps    | 17.1 Gbps        | 8K displays |
| 4     | 2.5 Gsps    | 22.8 Gbps        | Maximum bandwidth |

### Timing Constraints

For Xilinx UltraScale devices, use these timing constraints:

```tcl
# High-speed serial clocks
create_clock -period 0.4 [get_pins */MMCME4_BASE/CLKOUT0]
create_clock -period 1.6 [get_pins */MMCME4_BASE/CLKOUT1]

# C-PHY trio signals
set_property IOSTANDARD MIPI_DPHY_DCI [get_ports cphy_trio_*]
set_property DIFF_TERM_ADV TERM_100 [get_ports cphy_trio_*]

# Input delays for receiver
set_input_delay -clock [get_clocks rx_clock] -max 0.2 [get_ports cphy_trio_*_p]
set_input_delay -clock [get_clocks rx_clock] -min -0.2 [get_ports cphy_trio_*_p]
```

## Testing and Verification

### Built-in Test Patterns

The implementation includes several test patterns:

```scala
val testSystem = CPhyTestSystem(CPhySystemConfig(
  dataWidth = 32,
  trioCount = 2
))

// Pattern types:
// 0: Incrementing counter
// 1: Alternating 0x00000000/0xFFFFFFFF
// 2: Walking ones
// 3: PRBS pattern
testSystem.io.patternSelect := 0
```

### Running Tests

```scala
// Compile and run tests
sbt "testOnly spinal.lib.com.mipi.cphy.CPhyTester"
```

## Integration Examples

### Camera Interface

```scala
class CameraCPhyInterface extends Component {
  val io = new Bundle {
    val pixelData = slave(Stream(Bits(32 bits)))
    val cphyOut = Vec(master(CPhyTrio()), 2)
    val refClock = in Bool()
    val enable = in Bool()
    val ready = out Bool()
  }
  
  val cphy = CPhyTxUltraScale(CPhyTxConfig(
    dataWidth = 32,
    trioCount = 2,
    symbolRate = 2.28 GHz
  ))
  
  cphy.io.dataIn <> io.pixelData
  cphy.io.refClock := io.refClock
  cphy.io.enable := io.enable
  
  for(i <- 0 until 2) {
    io.cphyOut(i) <> cphy.io.triosOut(i)
  }
  
  io.ready := cphy.io.ready
}
```

### Display Interface

```scala
class DisplayCPhyInterface extends Component {
  val io = new Bundle {
    val cphyIn = Vec(slave(CPhyTrio()), 3)
    val displayData = master(Stream(Bits(24 bits)))
    val refClock = in Bool()
    val enable = in Bool()
    val locked = out Bool()
  }
  
  val cphy = CPhyRxUltraScale(CPhyRxConfig(
    dataWidth = 24,
    trioCount = 3,
    symbolRate = 2.28 GHz
  ))
  
  for(i <- 0 until 3) {
    cphy.io.triosIn(i) <> io.cphyIn(i)
  }
  cphy.io.refClock := io.refClock
  cphy.io.enable := io.enable
  
  io.displayData <> cphy.io.dataOut
  io.locked := cphy.io.locked
}
```

## Troubleshooting

### Common Issues

1. **Clock Domain Crossing**: Ensure proper clock domain crossing for data paths
2. **Timing Closure**: Use appropriate timing constraints for high-speed signals
3. **Signal Integrity**: Verify PCB layout meets MIPI C-PHY requirements
4. **Calibration**: Allow sufficient time for PLL lock and calibration

### Debug Features

- Built-in error counters
- Signal strength monitoring
- Pattern generators for testing
- Loopback modes for verification

## API Reference

### Core Classes

- `CPhyConfig`: Basic configuration parameters
- `CPhyTrio`: 3-wire trio interface
- `CPhyEncoder/CPhyDecoder`: Symbol encoding/decoding
- `CPhyTransmitter/CPhyReceiver`: Complete TX/RX implementations
- `CPhySystem`: Full transceiver system

### UltraScale Optimized Classes

- `CPhyTxUltraScale`: Optimized transmitter using UltraScale primitives
- `CPhyRxUltraScale`: Optimized receiver using UltraScale primitives
- UltraScale primitive wrappers: `ISERDES3`, `OSERDES3`, `MMCME4_BASE`

## License

This implementation is part of SpinalHDL and follows the same licensing terms.
