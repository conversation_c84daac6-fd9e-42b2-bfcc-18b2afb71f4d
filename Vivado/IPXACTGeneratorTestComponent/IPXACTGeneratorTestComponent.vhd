-- Generator : SpinalHDL dev    git head : 4e4e7ea3f9def0c5de5394c526f14ca4446eab60
-- Component : IPXACTGeneratorTestComponent
-- Git hash  : 4e4e7ea3f9def0c5de5394c526f14ca4446eab60

library IEEE;
use IEEE.STD_LOGIC_1164.ALL;
use IEEE.NUMERIC_STD.all;

package pkg_enum is

end pkg_enum;

library IEEE;
use ieee.std_logic_1164.all;
use ieee.numeric_std.all;
use ieee.math_real.all;

package pkg_scala2hdl is
  function pkg_extract (that : std_logic_vector; bitId : integer) return std_logic;
  function pkg_extract (that : std_logic_vector; base : unsigned; size : integer) return std_logic_vector;
  function pkg_cat (a : std_logic_vector; b : std_logic_vector) return std_logic_vector;
  function pkg_not (value : std_logic_vector) return std_logic_vector;
  function pkg_extract (that : unsigned; bitId : integer) return std_logic;
  function pkg_extract (that : unsigned; base : unsigned; size : integer) return unsigned;
  function pkg_cat (a : unsigned; b : unsigned) return unsigned;
  function pkg_not (value : unsigned) return unsigned;
  function pkg_extract (that : signed; bitId : integer) return std_logic;
  function pkg_extract (that : signed; base : unsigned; size : integer) return signed;
  function pkg_cat (a : signed; b : signed) return signed;
  function pkg_not (value : signed) return signed;

  function pkg_mux (sel : std_logic; one : std_logic; zero : std_logic) return std_logic;
  function pkg_mux (sel : std_logic; one : std_logic_vector; zero : std_logic_vector) return std_logic_vector;
  function pkg_mux (sel : std_logic; one : unsigned; zero : unsigned) return unsigned;
  function pkg_mux (sel : std_logic; one : signed; zero : signed) return signed;

  function pkg_toStdLogic (value : boolean) return std_logic;
  function pkg_toStdLogicVector (value : std_logic) return std_logic_vector;
  function pkg_toUnsigned (value : std_logic) return unsigned;
  function pkg_toSigned (value : std_logic) return signed;
  function pkg_stdLogicVector (lit : std_logic_vector) return std_logic_vector;
  function pkg_unsigned (lit : unsigned) return unsigned;
  function pkg_signed (lit : signed) return signed;

  function pkg_resize (that : std_logic_vector; width : integer) return std_logic_vector;
  function pkg_resize (that : unsigned; width : integer) return unsigned;
  function pkg_resize (that : signed; width : integer) return signed;

  function pkg_extract (that : std_logic_vector; high : integer; low : integer) return std_logic_vector;
  function pkg_extract (that : unsigned; high : integer; low : integer) return unsigned;
  function pkg_extract (that : signed; high : integer; low : integer) return signed;

  function pkg_shiftRight (that : std_logic_vector; size : natural) return std_logic_vector;
  function pkg_shiftRight (that : std_logic_vector; size : unsigned) return std_logic_vector;
  function pkg_shiftLeft (that : std_logic_vector; size : natural) return std_logic_vector;
  function pkg_shiftLeft (that : std_logic_vector; size : unsigned) return std_logic_vector;

  function pkg_shiftRight (that : unsigned; size : natural) return unsigned;
  function pkg_shiftRight (that : unsigned; size : unsigned) return unsigned;
  function pkg_shiftLeft (that : unsigned; size : natural) return unsigned;
  function pkg_shiftLeft (that : unsigned; size : unsigned) return unsigned;

  function pkg_shiftRight (that : signed; size : natural) return signed;
  function pkg_shiftRight (that : signed; size : unsigned) return signed;
  function pkg_shiftLeft (that : signed; size : natural) return signed;
  function pkg_shiftLeft (that : signed; size : unsigned; w : integer) return signed;

  function pkg_rotateLeft (that : std_logic_vector; size : unsigned) return std_logic_vector;
end  pkg_scala2hdl;

package body pkg_scala2hdl is
  function pkg_extract (that : std_logic_vector; bitId : integer) return std_logic is
    alias temp : std_logic_vector(that'length-1 downto 0) is that;
  begin
    if bitId >= temp'length then
      return 'U';
    end if;
    return temp(bitId);
  end pkg_extract;

  function pkg_extract (that : std_logic_vector; base : unsigned; size : integer) return std_logic_vector is
    alias temp : std_logic_vector(that'length-1 downto 0) is that;    constant elementCount : integer := temp'length - size + 1;
    type tableType is array (0 to elementCount-1) of std_logic_vector(size-1 downto 0);
    variable table : tableType;
  begin
    for i in 0 to elementCount-1 loop
      table(i) := temp(i + size - 1 downto i);
    end loop;
    if base + size >= elementCount then
      return (size-1 downto 0 => 'U');
    end if;
    return table(to_integer(base));
  end pkg_extract;

  function pkg_cat (a : std_logic_vector; b : std_logic_vector) return std_logic_vector is
    variable cat : std_logic_vector(a'length + b'length-1 downto 0);
  begin
    cat := a & b;
    return cat;
  end pkg_cat;

  function pkg_not (value : std_logic_vector) return std_logic_vector is
    variable ret : std_logic_vector(value'length-1 downto 0);
  begin
    ret := not value;
    return ret;
  end pkg_not;

  function pkg_extract (that : unsigned; bitId : integer) return std_logic is
    alias temp : unsigned(that'length-1 downto 0) is that;
  begin
    if bitId >= temp'length then
      return 'U';
    end if;
    return temp(bitId);
  end pkg_extract;

  function pkg_extract (that : unsigned; base : unsigned; size : integer) return unsigned is
    alias temp : unsigned(that'length-1 downto 0) is that;    constant elementCount : integer := temp'length - size + 1;
    type tableType is array (0 to elementCount-1) of unsigned(size-1 downto 0);
    variable table : tableType;
  begin
    for i in 0 to elementCount-1 loop
      table(i) := temp(i + size - 1 downto i);
    end loop;
    if base + size >= elementCount then
      return (size-1 downto 0 => 'U');
    end if;
    return table(to_integer(base));
  end pkg_extract;

  function pkg_cat (a : unsigned; b : unsigned) return unsigned is
    variable cat : unsigned(a'length + b'length-1 downto 0);
  begin
    cat := a & b;
    return cat;
  end pkg_cat;

  function pkg_not (value : unsigned) return unsigned is
    variable ret : unsigned(value'length-1 downto 0);
  begin
    ret := not value;
    return ret;
  end pkg_not;

  function pkg_extract (that : signed; bitId : integer) return std_logic is
    alias temp : signed(that'length-1 downto 0) is that;
  begin
    if bitId >= temp'length then
      return 'U';
    end if;
    return temp(bitId);
  end pkg_extract;

  function pkg_extract (that : signed; base : unsigned; size : integer) return signed is
    alias temp : signed(that'length-1 downto 0) is that;    constant elementCount : integer := temp'length - size + 1;
    type tableType is array (0 to elementCount-1) of signed(size-1 downto 0);
    variable table : tableType;
  begin
    for i in 0 to elementCount-1 loop
      table(i) := temp(i + size - 1 downto i);
    end loop;
    if base + size >= elementCount then
      return (size-1 downto 0 => 'U');
    end if;
    return table(to_integer(base));
  end pkg_extract;

  function pkg_cat (a : signed; b : signed) return signed is
    variable cat : signed(a'length + b'length-1 downto 0);
  begin
    cat := a & b;
    return cat;
  end pkg_cat;

  function pkg_not (value : signed) return signed is
    variable ret : signed(value'length-1 downto 0);
  begin
    ret := not value;
    return ret;
  end pkg_not;


  -- unsigned shifts
  function pkg_shiftRight (that : unsigned; size : natural) return unsigned is
    variable ret : unsigned(that'length-1 downto 0);
  begin
    if size >= that'length then
      return "";
    else
      ret := shift_right(that,size);
      return ret(that'length-1-size downto 0);
    end if;
  end pkg_shiftRight;

  function pkg_shiftRight (that : unsigned; size : unsigned) return unsigned is
    variable ret : unsigned(that'length-1 downto 0);
  begin
    ret := shift_right(that,to_integer(size));
    return ret;
  end pkg_shiftRight;

  function pkg_shiftLeft (that : unsigned; size : natural) return unsigned is
  begin
    return shift_left(resize(that,that'length + size),size);
  end pkg_shiftLeft;

  function pkg_shiftLeft (that : unsigned; size : unsigned) return unsigned is
  begin
    return shift_left(resize(that,that'length + 2**size'length - 1),to_integer(size));
  end pkg_shiftLeft;

  -- std_logic_vector shifts
  function pkg_shiftRight (that : std_logic_vector; size : natural) return std_logic_vector is
  begin
    return std_logic_vector(pkg_shiftRight(unsigned(that),size));
  end pkg_shiftRight;

  function pkg_shiftRight (that : std_logic_vector; size : unsigned) return std_logic_vector is
  begin
    return std_logic_vector(pkg_shiftRight(unsigned(that),size));
  end pkg_shiftRight;

  function pkg_shiftLeft (that : std_logic_vector; size : natural) return std_logic_vector is
  begin
    return std_logic_vector(pkg_shiftLeft(unsigned(that),size));
  end pkg_shiftLeft;

  function pkg_shiftLeft (that : std_logic_vector; size : unsigned) return std_logic_vector is
  begin
    return std_logic_vector(pkg_shiftLeft(unsigned(that),size));
  end pkg_shiftLeft;

  -- signed shifts
  function pkg_shiftRight (that : signed; size : natural) return signed is
  begin
    return signed(pkg_shiftRight(unsigned(that),size));
  end pkg_shiftRight;

  function pkg_shiftRight (that : signed; size : unsigned) return signed is
  begin
    return shift_right(that,to_integer(size));
  end pkg_shiftRight;

  function pkg_shiftLeft (that : signed; size : natural) return signed is
  begin
    return signed(pkg_shiftLeft(unsigned(that),size));
  end pkg_shiftLeft;

  function pkg_shiftLeft (that : signed; size : unsigned; w : integer) return signed is
  begin
    return shift_left(resize(that,w),to_integer(size));
  end pkg_shiftLeft;

  function pkg_rotateLeft (that : std_logic_vector; size : unsigned) return std_logic_vector is
  begin
    return std_logic_vector(rotate_left(unsigned(that),to_integer(size)));
  end pkg_rotateLeft;

  function pkg_extract (that : std_logic_vector; high : integer; low : integer) return std_logic_vector is
    alias temp : std_logic_vector(that'length-1 downto 0) is that;
  begin
    return temp(high downto low);
  end pkg_extract;

  function pkg_extract (that : unsigned; high : integer; low : integer) return unsigned is
    alias temp : unsigned(that'length-1 downto 0) is that;
  begin
    return temp(high downto low);
  end pkg_extract;

  function pkg_extract (that : signed; high : integer; low : integer) return signed is
    alias temp : signed(that'length-1 downto 0) is that;
  begin
    return temp(high downto low);
  end pkg_extract;

  function pkg_mux (sel : std_logic; one : std_logic; zero : std_logic) return std_logic is
  begin
    if sel = '1' then
      return one;
    else
      return zero;
    end if;
  end pkg_mux;

  function pkg_mux (sel : std_logic; one : std_logic_vector; zero : std_logic_vector) return std_logic_vector is
    variable ret : std_logic_vector(zero'range);
  begin
    if sel = '1' then
      ret := one;
    else
      ret := zero;
    end if;
    return ret;
  end pkg_mux;

  function pkg_mux (sel : std_logic; one : unsigned; zero : unsigned) return unsigned is
    variable ret : unsigned(zero'range);
  begin
    if sel = '1' then
      ret := one;
    else
      ret := zero;
    end if;
    return ret;
  end pkg_mux;

  function pkg_mux (sel : std_logic; one : signed; zero : signed) return signed is
    variable ret : signed(zero'range);
  begin
    if sel = '1' then
      ret := one;
    else
      ret := zero;
    end if;
    return ret;
  end pkg_mux;

  function pkg_toStdLogic (value : boolean) return std_logic is
  begin
    if value = true then
      return '1';
    else
      return '0';
    end if;
  end pkg_toStdLogic;

  function pkg_toStdLogicVector (value : std_logic) return std_logic_vector is
    variable ret : std_logic_vector(0 downto 0);
  begin
    ret(0) := value;
    return ret;
  end pkg_toStdLogicVector;

  function pkg_toUnsigned (value : std_logic) return unsigned is
    variable ret : unsigned(0 downto 0);
  begin
    ret(0) := value;
    return ret;
  end pkg_toUnsigned;

  function pkg_toSigned (value : std_logic) return signed is
    variable ret : signed(0 downto 0);
  begin
    ret(0) := value;
    return ret;
  end pkg_toSigned;

  function pkg_stdLogicVector (lit : std_logic_vector) return std_logic_vector is
    alias ret : std_logic_vector(lit'length-1 downto 0) is lit;
  begin
    return std_logic_vector(ret);
  end pkg_stdLogicVector;

  function pkg_unsigned (lit : unsigned) return unsigned is
    alias ret : unsigned(lit'length-1 downto 0) is lit;
  begin
    return unsigned(ret);
  end pkg_unsigned;

  function pkg_signed (lit : signed) return signed is
    alias ret : signed(lit'length-1 downto 0) is lit;
  begin
    return signed(ret);
  end pkg_signed;

  function pkg_resize (that : std_logic_vector; width : integer) return std_logic_vector is
  begin
    return std_logic_vector(resize(unsigned(that),width));
  end pkg_resize;

  function pkg_resize (that : unsigned; width : integer) return unsigned is
    variable ret : unsigned(width-1 downto 0);
  begin
    if that'length = 0 then
       ret := (others => '0');
    else
       ret := resize(that,width);
    end if;
    return ret;
  end pkg_resize;
  function pkg_resize (that : signed; width : integer) return signed is
    alias temp : signed(that'length-1 downto 0) is that;
    variable ret : signed(width-1 downto 0);
  begin
    if temp'length = 0 then
       ret := (others => '0');
    elsif temp'length >= width then
       ret := temp(width-1 downto 0);
    else
       ret := resize(temp,width);
    end if;
    return ret;
  end pkg_resize;
end pkg_scala2hdl;


library ieee;
use ieee.std_logic_1164.all;
use ieee.numeric_std.all;

library work;
use work.pkg_scala2hdl.all;
use work.all;
use work.pkg_enum.all;


entity InnerInnerApbModule is
  port(
    io_apb_out_PADDR : out unsigned(11 downto 0);
    io_apb_out_PSEL : out std_logic_vector(0 downto 0);
    io_apb_out_PENABLE : out std_logic;
    io_apb_out_PREADY : in std_logic;
    io_apb_out_PWRITE : out std_logic;
    io_apb_out_PWDATA : out std_logic_vector(31 downto 0);
    io_apb_out_PRDATA : in std_logic_vector(31 downto 0);
    io_apb_out_PSLVERROR : in std_logic;
    io_apb_in_PADDR : in unsigned(11 downto 0);
    io_apb_in_PSEL : in std_logic_vector(0 downto 0);
    io_apb_in_PENABLE : in std_logic;
    io_apb_in_PREADY : out std_logic;
    io_apb_in_PWRITE : in std_logic;
    io_apb_in_PWDATA : in std_logic_vector(31 downto 0);
    io_apb_in_PRDATA : out std_logic_vector(31 downto 0);
    io_apb_in_PSLVERROR : out std_logic
  );

end InnerInnerApbModule;

architecture arch of InnerInnerApbModule is

begin
  io_apb_out_PADDR <= io_apb_in_PADDR;
  io_apb_out_PSEL <= io_apb_in_PSEL;
  io_apb_out_PENABLE <= io_apb_in_PENABLE;
  io_apb_in_PREADY <= io_apb_out_PREADY;
  io_apb_out_PWRITE <= io_apb_in_PWRITE;
  io_apb_out_PWDATA <= io_apb_in_PWDATA;
  io_apb_in_PRDATA <= io_apb_out_PRDATA;
  io_apb_in_PSLVERROR <= io_apb_out_PSLVERROR;
end arch;

library ieee;
use ieee.std_logic_1164.all;
use ieee.numeric_std.all;

library work;
use work.pkg_scala2hdl.all;
use work.all;
use work.pkg_enum.all;


entity InnerApbModule is
  port(
    io_apb_out_PADDR : out unsigned(11 downto 0);
    io_apb_out_PSEL : out std_logic_vector(0 downto 0);
    io_apb_out_PENABLE : out std_logic;
    io_apb_out_PREADY : in std_logic;
    io_apb_out_PWRITE : out std_logic;
    io_apb_out_PWDATA : out std_logic_vector(31 downto 0);
    io_apb_out_PRDATA : in std_logic_vector(31 downto 0);
    io_apb_out_PSLVERROR : in std_logic;
    io_apb_in_PADDR : in unsigned(11 downto 0);
    io_apb_in_PSEL : in std_logic_vector(0 downto 0);
    io_apb_in_PENABLE : in std_logic;
    io_apb_in_PREADY : out std_logic;
    io_apb_in_PWRITE : in std_logic;
    io_apb_in_PWDATA : in std_logic_vector(31 downto 0);
    io_apb_in_PRDATA : out std_logic_vector(31 downto 0);
    io_apb_in_PSLVERROR : out std_logic
  );

end InnerApbModule;

architecture arch of InnerApbModule is
  signal innerInnerApbModule_1_io_apb_out_PADDR : unsigned(11 downto 0);
  signal innerInnerApbModule_1_io_apb_out_PSEL : std_logic_vector(0 downto 0);
  signal innerInnerApbModule_1_io_apb_out_PENABLE : std_logic;
  signal innerInnerApbModule_1_io_apb_out_PWRITE : std_logic;
  signal innerInnerApbModule_1_io_apb_out_PWDATA : std_logic_vector(31 downto 0);
  signal innerInnerApbModule_1_io_apb_in_PREADY : std_logic;
  signal innerInnerApbModule_1_io_apb_in_PRDATA : std_logic_vector(31 downto 0);
  signal innerInnerApbModule_1_io_apb_in_PSLVERROR : std_logic;

begin
  innerInnerApbModule_1 : entity work.InnerInnerApbModule
    port map ( 
      io_apb_out_PADDR => innerInnerApbModule_1_io_apb_out_PADDR,
      io_apb_out_PSEL => innerInnerApbModule_1_io_apb_out_PSEL,
      io_apb_out_PENABLE => innerInnerApbModule_1_io_apb_out_PENABLE,
      io_apb_out_PREADY => io_apb_out_PREADY,
      io_apb_out_PWRITE => innerInnerApbModule_1_io_apb_out_PWRITE,
      io_apb_out_PWDATA => innerInnerApbModule_1_io_apb_out_PWDATA,
      io_apb_out_PRDATA => io_apb_out_PRDATA,
      io_apb_out_PSLVERROR => io_apb_out_PSLVERROR,
      io_apb_in_PADDR => io_apb_in_PADDR,
      io_apb_in_PSEL => io_apb_in_PSEL,
      io_apb_in_PENABLE => io_apb_in_PENABLE,
      io_apb_in_PREADY => innerInnerApbModule_1_io_apb_in_PREADY,
      io_apb_in_PWRITE => io_apb_in_PWRITE,
      io_apb_in_PWDATA => io_apb_in_PWDATA,
      io_apb_in_PRDATA => innerInnerApbModule_1_io_apb_in_PRDATA,
      io_apb_in_PSLVERROR => innerInnerApbModule_1_io_apb_in_PSLVERROR 
    );
  io_apb_in_PREADY <= innerInnerApbModule_1_io_apb_in_PREADY;
  io_apb_in_PRDATA <= innerInnerApbModule_1_io_apb_in_PRDATA;
  io_apb_in_PSLVERROR <= innerInnerApbModule_1_io_apb_in_PSLVERROR;
  io_apb_out_PADDR <= innerInnerApbModule_1_io_apb_out_PADDR;
  io_apb_out_PSEL <= innerInnerApbModule_1_io_apb_out_PSEL;
  io_apb_out_PENABLE <= innerInnerApbModule_1_io_apb_out_PENABLE;
  io_apb_out_PWRITE <= innerInnerApbModule_1_io_apb_out_PWRITE;
  io_apb_out_PWDATA <= innerInnerApbModule_1_io_apb_out_PWDATA;
end arch;

library ieee;
use ieee.std_logic_1164.all;
use ieee.numeric_std.all;

library work;
use work.pkg_scala2hdl.all;
use work.all;
use work.pkg_enum.all;


entity IPXACTGeneratorTestComponent is
  port(
    io_apb_out_PADDR : out unsigned(11 downto 0);
    io_apb_out_PSEL : out std_logic_vector(0 downto 0);
    io_apb_out_PENABLE : out std_logic;
    io_apb_out_PREADY : in std_logic;
    io_apb_out_PWRITE : out std_logic;
    io_apb_out_PWDATA : out std_logic_vector(31 downto 0);
    io_apb_out_PRDATA : in std_logic_vector(31 downto 0);
    io_apb_out_PSLVERROR : in std_logic;
    io_apb_in_PADDR : in unsigned(11 downto 0);
    io_apb_in_PSEL : in std_logic_vector(0 downto 0);
    io_apb_in_PENABLE : in std_logic;
    io_apb_in_PREADY : out std_logic;
    io_apb_in_PWRITE : in std_logic;
    io_apb_in_PWDATA : in std_logic_vector(31 downto 0);
    io_apb_in_PRDATA : out std_logic_vector(31 downto 0);
    io_apb_in_PSLVERROR : out std_logic;
    io_user_bus_in_aa : out std_logic;
    io_user_bus_in_bb : in std_logic;
    io_user_bus_out_aa : in std_logic;
    io_user_bus_out_bb : out std_logic;
    io_stream_out_valid : out std_logic;
    io_stream_out_ready : in std_logic;
    io_stream_out_payload : out std_logic;
    io_stream_in_valid : in std_logic;
    io_stream_in_ready : out std_logic;
    io_stream_in_payload : in std_logic;
    io_uart_out_txd : out std_logic;
    io_uart_out_rxd : in std_logic;
    io_uart_out_cts : in std_logic;
    io_uart_out_rts : out std_logic;
    io_uart_in_txd : in std_logic;
    io_uart_in_rxd : out std_logic;
    io_uart_in_cts : out std_logic;
    io_uart_in_rts : in std_logic;
    io_vga_out_vSync : out std_logic;
    io_vga_out_hSync : out std_logic;
    io_vga_out_colorEn : out std_logic;
    io_vga_out_color_r : out unsigned(7 downto 0);
    io_vga_out_color_g : out unsigned(7 downto 0);
    io_vga_out_color_b : out unsigned(7 downto 0);
    io_vga_in_vSync : in std_logic;
    io_vga_in_hSync : in std_logic;
    io_vga_in_colorEn : in std_logic;
    io_vga_in_color_r : in unsigned(7 downto 0);
    io_vga_in_color_g : in unsigned(7 downto 0);
    io_vga_in_color_b : in unsigned(7 downto 0);
    io_tri_read : out std_logic_vector(15 downto 0);
    io_tri_write : in std_logic_vector(15 downto 0);
    io_tri_writeEnable : in std_logic;
    io_analog : inout std_logic_vector(15 downto 0);
    io_bits_input : in std_logic_vector(7 downto 0);
    io_bits_output : out std_logic_vector(7 downto 0);
    clk : in std_logic;
    reset : in std_logic
  );

end IPXACTGeneratorTestComponent;

architecture arch of IPXACTGeneratorTestComponent is
  signal io_stream_in_ready_read_buffer : std_logic;
  signal innerApb3_io_apb_out_PADDR : unsigned(11 downto 0);
  signal innerApb3_io_apb_out_PSEL : std_logic_vector(0 downto 0);
  signal innerApb3_io_apb_out_PENABLE : std_logic;
  signal innerApb3_io_apb_out_PWRITE : std_logic;
  signal innerApb3_io_apb_out_PWDATA : std_logic_vector(31 downto 0);
  signal innerApb3_io_apb_in_PREADY : std_logic;
  signal innerApb3_io_apb_in_PRDATA : std_logic_vector(31 downto 0);
  signal innerApb3_io_apb_in_PSLVERROR : std_logic;

  signal zz_io_analog : std_logic;
  signal io_stream_in_m2sPipe_valid : std_logic;
  signal io_stream_in_m2sPipe_ready : std_logic;
  signal io_stream_in_m2sPipe_payload : std_logic;
  signal io_stream_in_rValid : std_logic;
  signal io_stream_in_rData : std_logic;
  signal when_Stream_l393 : std_logic;
begin
  io_stream_in_ready <= io_stream_in_ready_read_buffer;
  innerApb3 : entity work.InnerApbModule
    port map ( 
      io_apb_out_PADDR => innerApb3_io_apb_out_PADDR,
      io_apb_out_PSEL => innerApb3_io_apb_out_PSEL,
      io_apb_out_PENABLE => innerApb3_io_apb_out_PENABLE,
      io_apb_out_PREADY => io_apb_out_PREADY,
      io_apb_out_PWRITE => innerApb3_io_apb_out_PWRITE,
      io_apb_out_PWDATA => innerApb3_io_apb_out_PWDATA,
      io_apb_out_PRDATA => io_apb_out_PRDATA,
      io_apb_out_PSLVERROR => io_apb_out_PSLVERROR,
      io_apb_in_PADDR => io_apb_in_PADDR,
      io_apb_in_PSEL => io_apb_in_PSEL,
      io_apb_in_PENABLE => io_apb_in_PENABLE,
      io_apb_in_PREADY => innerApb3_io_apb_in_PREADY,
      io_apb_in_PWRITE => io_apb_in_PWRITE,
      io_apb_in_PWDATA => io_apb_in_PWDATA,
      io_apb_in_PRDATA => innerApb3_io_apb_in_PRDATA,
      io_apb_in_PSLVERROR => innerApb3_io_apb_in_PSLVERROR 
    );
  io_analog <= pkg_extract(io_tri_write,15,0) when zz_io_analog = '1' else (others => 'Z');
  process(io_tri_writeEnable)
  begin
    zz_io_analog <= pkg_toStdLogic(false);
    if io_tri_writeEnable = '1' then
      zz_io_analog <= pkg_toStdLogic(true);
    end if;
  end process;

  io_bits_output <= io_bits_input;
  io_tri_read <= io_analog;
  io_vga_out_vSync <= io_vga_in_vSync;
  io_vga_out_hSync <= io_vga_in_hSync;
  io_vga_out_colorEn <= io_vga_in_colorEn;
  io_vga_out_color_r <= io_vga_in_color_r;
  io_vga_out_color_g <= io_vga_in_color_g;
  io_vga_out_color_b <= io_vga_in_color_b;
  io_uart_out_txd <= io_uart_in_txd;
  io_uart_in_rxd <= io_uart_out_rxd;
  io_uart_in_cts <= io_uart_out_cts;
  io_uart_out_rts <= io_uart_in_rts;
  io_apb_in_PREADY <= innerApb3_io_apb_in_PREADY;
  io_apb_in_PRDATA <= innerApb3_io_apb_in_PRDATA;
  io_apb_in_PSLVERROR <= innerApb3_io_apb_in_PSLVERROR;
  io_apb_out_PADDR <= innerApb3_io_apb_out_PADDR;
  io_apb_out_PSEL <= innerApb3_io_apb_out_PSEL;
  io_apb_out_PENABLE <= innerApb3_io_apb_out_PENABLE;
  io_apb_out_PWRITE <= innerApb3_io_apb_out_PWRITE;
  io_apb_out_PWDATA <= innerApb3_io_apb_out_PWDATA;
  process(io_stream_in_m2sPipe_ready,when_Stream_l393)
  begin
    io_stream_in_ready_read_buffer <= io_stream_in_m2sPipe_ready;
    if when_Stream_l393 = '1' then
      io_stream_in_ready_read_buffer <= pkg_toStdLogic(true);
    end if;
  end process;

  when_Stream_l393 <= (not io_stream_in_m2sPipe_valid);
  io_stream_in_m2sPipe_valid <= io_stream_in_rValid;
  io_stream_in_m2sPipe_payload <= io_stream_in_rData;
  io_stream_out_valid <= io_stream_in_m2sPipe_valid;
  io_stream_in_m2sPipe_ready <= io_stream_out_ready;
  io_stream_out_payload <= io_stream_in_m2sPipe_payload;
  io_user_bus_in_aa <= io_user_bus_out_aa;
  io_user_bus_out_bb <= io_user_bus_in_bb;
  process(clk, reset)
  begin
    if reset = '1' then
      io_stream_in_rValid <= pkg_toStdLogic(false);
    elsif rising_edge(clk) then
      if io_stream_in_ready_read_buffer = '1' then
        io_stream_in_rValid <= io_stream_in_valid;
      end if;
    end if;
  end process;

  process(clk)
  begin
    if rising_edge(clk) then
      if io_stream_in_ready_read_buffer = '1' then
        io_stream_in_rData <= io_stream_in_payload;
      end if;
    end if;
  end process;

end arch;

