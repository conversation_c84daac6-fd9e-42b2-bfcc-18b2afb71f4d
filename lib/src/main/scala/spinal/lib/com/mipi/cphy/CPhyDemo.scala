package spinal.lib.com.mipi.cphy

import spinal.core._
import spinal.lib._

/**
 * Complete MIPI C-PHY Demo for Xilinx UltraScale
 * 
 * This demo generates a complete C-PHY system suitable for
 * implementation on Xilinx UltraScale devices like KCU105.
 */
object CPhyUltraScaleDemo extends App {
  
  /**
   * Top-level C-PHY demonstration system
   * Includes transmitter, receiver, test pattern generation,
   * and comprehensive monitoring capabilities.
   */
  class CPhyUltraScaleTop extends Component {
    val io = new Bundle {
      // Clock inputs
      val sys_clk_p = in Bool()      // 200 MHz differential system clock
      val sys_clk_n = in Bool()
      
      // Reset and control
      val cpu_reset = in Bool()       // CPU reset button
      val enable_sw = in Bool()       // Enable switch
      val test_mode_sw = in Bool()    // Test mode switch
      val pattern_sel = in UInt(4 bits)  // Pattern selection DIP switches
      
      // C-PHY differential outputs (SMA connectors)
      val cphy_tx_trio0_a_p = out Bool()
      val cphy_tx_trio0_a_n = out Bool()
      val cphy_tx_trio0_b_p = out Bool()
      val cphy_tx_trio0_b_n = out Bool()
      val cphy_tx_trio0_c_p = out Bool()
      val cphy_tx_trio0_c_n = out Bool()
      
      val cphy_tx_trio1_a_p = out Bool()
      val cphy_tx_trio1_a_n = out Bool()
      val cphy_tx_trio1_b_p = out Bool()
      val cphy_tx_trio1_b_n = out Bool()
      val cphy_tx_trio1_c_p = out Bool()
      val cphy_tx_trio1_c_n = out Bool()
      
      // C-PHY differential inputs (SMA connectors)
      val cphy_rx_trio0_a_p = in Bool()
      val cphy_rx_trio0_a_n = in Bool()
      val cphy_rx_trio0_b_p = in Bool()
      val cphy_rx_trio0_b_n = in Bool()
      val cphy_rx_trio0_c_p = in Bool()
      val cphy_rx_trio0_c_n = in Bool()
      
      val cphy_rx_trio1_a_p = in Bool()
      val cphy_rx_trio1_a_n = in Bool()
      val cphy_rx_trio1_b_p = in Bool()
      val cphy_rx_trio1_b_n = in Bool()
      val cphy_rx_trio1_c_p = in Bool()
      val cphy_rx_trio1_c_n = in Bool()
      
      // Status LEDs
      val led_system_ready = out Bool()
      val led_tx_ready = out Bool()
      val led_rx_locked = out Bool()
      val led_test_pass = out Bool()
      val led_error = out Bool()
      
      // Debug outputs
      val debug_clk_out = out Bool()
      val debug_data_valid = out Bool()
    }
    
    // Input clock buffer for differential clock
    val sys_clk = Bool()
    // In real implementation, would use IBUFGDS primitive
    sys_clk := io.sys_clk_p  // Simplified for demo
    
    // Reset synchronization
    val reset_sync = ResetCtrl.asyncAssertSyncDeassert(
      input = io.cpu_reset,
      clockDomain = ClockDomain(sys_clk),
      inputPolarity = HIGH,
      outputPolarity = HIGH
    )
    
    val systemDomain = ClockDomain(sys_clk, reset_sync)
    
    // Main system logic
    val systemArea = new ClockingArea(systemDomain) {
      
      // System configuration
      val cphyConfig = CPhySystemConfig(
        dataWidth = 32,
        trioCount = 2,
        symbolRate = 2.28 GHz,
        useUltraScale = true,
        enableLoopback = false
      )
      
      // Test pattern generator
      val patternGen = CPhyTestPatternGenerator(cphyConfig)
      patternGen.io.enable := io.enable_sw && io.test_mode_sw
      patternGen.io.patternSelect := io.pattern_sel
      
      // C-PHY system
      val cphySystem = CPhySystem(cphyConfig)
      cphySystem.io.txData <> patternGen.io.dataOut
      cphySystem.io.refClock := sys_clk
      cphySystem.io.enable := io.enable_sw
      cphySystem.io.reset := reset_sync
      
      // Convert single-ended trio signals to differential
      // (In real implementation, would use OBUFDS primitives)
      io.cphy_tx_trio0_a_p := cphySystem.io.trios(0).a
      io.cphy_tx_trio0_a_n := !cphySystem.io.trios(0).a
      io.cphy_tx_trio0_b_p := cphySystem.io.trios(0).b
      io.cphy_tx_trio0_b_n := !cphySystem.io.trios(0).b
      io.cphy_tx_trio0_c_p := cphySystem.io.trios(0).c
      io.cphy_tx_trio0_c_n := !cphySystem.io.trios(0).c
      
      io.cphy_tx_trio1_a_p := cphySystem.io.trios(1).a
      io.cphy_tx_trio1_a_n := !cphySystem.io.trios(1).a
      io.cphy_tx_trio1_b_p := cphySystem.io.trios(1).b
      io.cphy_tx_trio1_b_n := !cphySystem.io.trios(1).b
      io.cphy_tx_trio1_c_p := cphySystem.io.trios(1).c
      io.cphy_tx_trio1_c_n := !cphySystem.io.trios(1).c
      
      // Convert differential input signals to single-ended
      // (In real implementation, would use IBUFDS primitives)
      cphySystem.io.trios(0).a := io.cphy_rx_trio0_a_p
      cphySystem.io.trios(0).b := io.cphy_rx_trio0_b_p
      cphySystem.io.trios(0).c := io.cphy_rx_trio0_c_p
      
      cphySystem.io.trios(1).a := io.cphy_rx_trio1_a_p
      cphySystem.io.trios(1).b := io.cphy_rx_trio1_b_p
      cphySystem.io.trios(1).c := io.cphy_rx_trio1_c_p
      
      // Data checker for test mode
      val dataChecker = CPhyDataChecker(cphyConfig)
      dataChecker.io.dataIn <> cphySystem.io.rxData
      dataChecker.io.expectedPattern := io.pattern_sel
      dataChecker.io.enable := io.enable_sw && io.test_mode_sw
      
      // Performance monitoring
      val perfMonitor = new Area {
        val throughputCounter = Reg(UInt(32 bits)) init(0)
        val timeCounter = Reg(UInt(32 bits)) init(0)
        val errorRateCalc = Reg(UInt(16 bits)) init(0)
        
        timeCounter := timeCounter + 1
        
        when(cphySystem.io.rxData.fire) {
          throughputCounter := throughputCounter + 1
        }
        
        // Update error rate every second (assuming 200 MHz clock)
        when(timeCounter === 200000000) {
          errorRateCalc := dataChecker.io.errorRate
          throughputCounter := 0
          timeCounter := 0
        }
      }
      
      // LED status indicators
      val ledController = new Area {
        val blinkCounter = Reg(UInt(26 bits)) init(0)
        blinkCounter := blinkCounter + 1
        val blink = blinkCounter.msb
        
        io.led_system_ready := cphySystem.io.systemReady
        io.led_tx_ready := cphySystem.io.txReady
        io.led_rx_locked := cphySystem.io.rxLocked
        io.led_test_pass := io.test_mode_sw && (dataChecker.io.errorCount === 0) && (dataChecker.io.totalCount > 1000)
        io.led_error := io.test_mode_sw && (dataChecker.io.errorRate > 10) && blink
      }
      
      // Debug outputs
      io.debug_clk_out := sys_clk
      io.debug_data_valid := cphySystem.io.rxData.valid
    }
  }
  
  // Generate the design with appropriate configuration
  val config = SpinalConfig(
    targetDirectory = "rtl/cphy_demo",
    defaultConfigForClockDomains = ClockDomainConfig(
      clockEdge = RISING,
      resetKind = ASYNC,
      resetActiveLevel = HIGH
    ),
    defaultClockDomainFrequency = FixedFrequency(200 MHz),
    onlyStdLogicVectorAtTopLevelIo = true,
    genVhdlPkg = true
  )
  
  config.generateVerilog(new CPhyUltraScaleTop)
  
  // Generate timing constraints file
  val xdcContent = s"""
# Clock constraints
create_clock -period 5.000 -name sys_clk [get_ports sys_clk_p]
set_property IOSTANDARD DIFF_SSTL15 [get_ports sys_clk_p]
set_property IOSTANDARD DIFF_SSTL15 [get_ports sys_clk_n]

# C-PHY high-speed clocks (generated internally)
create_generated_clock -name cphy_hs_clk -source [get_pins */MMCME4_BASE/CLKIN1] -multiply_by 10 [get_pins */MMCME4_BASE/CLKOUT0]
create_generated_clock -name cphy_word_clk -source [get_pins */MMCME4_BASE/CLKIN1] -multiply_by 10 -divide_by 4 [get_pins */MMCME4_BASE/CLKOUT1]

# C-PHY differential I/O constraints
set_property IOSTANDARD MIPI_DPHY_DCI [get_ports cphy_tx_trio*]
set_property IOSTANDARD MIPI_DPHY_DCI [get_ports cphy_rx_trio*]
set_property DIFF_TERM_ADV TERM_100 [get_ports cphy_rx_trio*]

# Control I/O constraints
set_property IOSTANDARD LVCMOS18 [get_ports cpu_reset]
set_property IOSTANDARD LVCMOS18 [get_ports enable_sw]
set_property IOSTANDARD LVCMOS18 [get_ports test_mode_sw]
set_property IOSTANDARD LVCMOS18 [get_ports pattern_sel*]
set_property IOSTANDARD LVCMOS18 [get_ports led_*]
set_property IOSTANDARD LVCMOS18 [get_ports debug_*]

# Timing constraints for C-PHY signals
set_input_delay -clock cphy_word_clk -max 0.5 [get_ports cphy_rx_trio*]
set_input_delay -clock cphy_word_clk -min -0.5 [get_ports cphy_rx_trio*]
set_output_delay -clock cphy_word_clk -max 0.5 [get_ports cphy_tx_trio*]
set_output_delay -clock cphy_word_clk -min -0.5 [get_ports cphy_tx_trio*]

# False paths for control signals
set_false_path -from [get_ports cpu_reset]
set_false_path -from [get_ports enable_sw]
set_false_path -from [get_ports test_mode_sw]
set_false_path -from [get_ports pattern_sel*]
set_false_path -to [get_ports led_*]
set_false_path -to [get_ports debug_*]
"""
  
  // Write XDC file
  val xdcFile = new java.io.FileWriter("rtl/cphy_demo/CPhyUltraScaleTop.xdc")
  xdcFile.write(xdcContent)
  xdcFile.close()
  
  println("✅ C-PHY UltraScale demo generated successfully!")
  println("📁 Files generated in: rtl/cphy_demo/")
  println("📋 Verilog: CPhyUltraScaleTop.v")
  println("📋 Constraints: CPhyUltraScaleTop.xdc")
  println("\n🚀 Ready for Vivado synthesis and implementation!")
  
  // Print summary
  println("\n📊 Design Summary:")
  println("- Data Width: 32 bits")
  println("- Trio Count: 2")
  println("- Symbol Rate: 2.28 Gsps per trio")
  println("- Total Bandwidth: 11.4 Gbps")
  println("- Target Device: Xilinx UltraScale")
  println("- Test Patterns: 4 built-in patterns")
  println("- Monitoring: Error rate, throughput, status LEDs")
}

/**
 * Generate all C-PHY examples and documentation
 */
object GenerateAllCPhyExamples extends App {
  println("🔧 Generating all C-PHY examples...")
  
  // Generate basic examples
  CPhyBasicTxExample.main(Array())
  CPhyBasicRxExample.main(Array())
  CPhyCompleteSystemExample.main(Array())
  CPhyAxi4StreamExample.main(Array())
  
  // Generate UltraScale demo
  CPhyUltraScaleDemo.main(Array())
  
  println("\n✅ All C-PHY examples generated successfully!")
  println("\n📚 Available examples:")
  println("1. Basic Transmitter (CameraCPhyInterface)")
  println("2. Basic Receiver (DisplayCPhyInterface)")
  println("3. Complete System (CPhyTestBench)")
  println("4. AXI4-Stream Bridge (CPhyAxi4StreamBridge)")
  println("5. UltraScale Demo (CPhyUltraScaleTop)")
  
  println("\n📖 Documentation:")
  println("- Implementation Guide: docs/MIPI_CPHY_SpinalHDL_Guide.md")
  println("- Test Suite: tester/src/test/scala/spinal/lib/com/mipi/cphy/CPhyTester.scala")
  
  println("\n🎯 Next Steps:")
  println("1. Review the generated Verilog files")
  println("2. Run the test suite: sbt 'testOnly spinal.lib.com.mipi.cphy.CPhyTester'")
  println("3. Synthesize with Vivado using the provided constraints")
  println("4. Customize for your specific application")
}
