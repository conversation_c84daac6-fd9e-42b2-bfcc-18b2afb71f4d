package spinal.lib.com.mipi.cphy

import spinal.core._
import spinal.lib._

/**
 * Example: Basic C-PHY Transmitter for Camera Interface
 * 
 * This example shows how to create a simple C-PHY transmitter
 * for connecting a camera sensor to an FPGA.
 */
object CPhyBasicTxExample extends App {
  
  class CameraCPhyInterface extends Component {
    val io = new Bundle {
      // Camera parallel interface
      val pixelData = slave(Stream(Bits(32 bits)))
      
      // C-PHY output (2 trios for higher bandwidth)
      val cphyTrios = Vec(master(CPhyTrio()), 2)
      
      // Clock and control
      val refClock = in Bool()
      val enable = in Bool()
      val reset = in Bool()
      
      // Status
      val ready = out Bool()
      val locked = out Bool()
    }
    
    // C-PHY transmitter configuration
    val cphyConfig = CPhyTxConfig(
      dataWidth = 32,        // 32-bit pixel data
      trioCount = 2,         // 2 trios for 4.56 Gbps total
      symbolRate = 2.28 GHz, // Per trio symbol rate
      enablePreEmphasis = true,
      enableCalibration = true
    )
    
    // Instantiate C-PHY transmitter
    val cphyTx = CPhyTxUltraScale(cphyConfig)
    
    // Connect interfaces
    cphyTx.io.dataIn <> io.pixelData
    cphyTx.io.refClock := io.refClock
    cphyTx.io.enable := io.enable
    cphyTx.io.reset := io.reset
    
    // Connect trio outputs
    for(i <- 0 until 2) {
      io.cphyTrios(i) <> cphyTx.io.triosOut(i)
    }
    
    // Status outputs
    io.ready := cphyTx.io.ready
    io.locked := cphyTx.io.locked
  }
  
  // Generate Verilog
  SpinalConfig(
    targetDirectory = "rtl/cphy_examples",
    defaultConfigForClockDomains = ClockDomainConfig(
      clockEdge = RISING,
      resetKind = ASYNC,
      resetActiveLevel = HIGH
    )
  ).generateVerilog(new CameraCPhyInterface)
  
  println("✓ Basic C-PHY TX example generated")
}

/**
 * Example: C-PHY Receiver for Display Interface
 * 
 * This example shows how to create a C-PHY receiver
 * for receiving data from a display controller.
 */
object CPhyBasicRxExample extends App {
  
  class DisplayCPhyInterface extends Component {
    val io = new Bundle {
      // C-PHY input (3 trios for maximum bandwidth)
      val cphyTrios = Vec(slave(CPhyTrio()), 3)
      
      // Display parallel interface
      val displayData = master(Stream(Bits(24 bits)))
      
      // Clock and control
      val refClock = in Bool()
      val enable = in Bool()
      val reset = in Bool()
      
      // Status and monitoring
      val locked = out Bool()
      val dataValid = out Bool()
      val errorRate = out UInt(16 bits)
      val signalStrength = out UInt(8 bits)
    }
    
    // C-PHY receiver configuration
    val cphyConfig = CPhyRxConfig(
      dataWidth = 24,        // 24-bit RGB data
      trioCount = 3,         // 3 trios for maximum bandwidth
      symbolRate = 2.28 GHz, // Per trio symbol rate
      enableCDR = true,
      enableEqualizer = true
    )
    
    // Instantiate C-PHY receiver with AGC
    val cphyRx = CPhyRxWithAGC(cphyConfig)
    
    // Connect interfaces
    for(i <- 0 until 3) {
      cphyRx.io.triosIn(i) <> io.cphyTrios(i)
    }
    cphyRx.io.refClock := io.refClock
    cphyRx.io.enable := io.enable
    cphyRx.io.reset := io.reset
    
    // Connect output
    io.displayData <> cphyRx.io.dataOut
    
    // Status outputs
    io.locked := cphyRx.io.locked
    io.dataValid := io.displayData.valid
    io.signalStrength := cphyRx.io.signalStrength
    
    // Simple error rate calculation
    val errorCounter = Reg(UInt(16 bits)) init(0)
    val totalCounter = Reg(UInt(16 bits)) init(0)
    
    when(io.displayData.fire) {
      totalCounter := totalCounter + 1
      // Simplified error detection (would be more sophisticated in practice)
      when(io.displayData.payload === 0) {
        errorCounter := errorCounter + 1
      }
    }
    
    io.errorRate := (errorCounter * 1000) / (totalCounter + 1)
  }
  
  // Generate Verilog
  SpinalConfig(
    targetDirectory = "rtl/cphy_examples",
    defaultConfigForClockDomains = ClockDomainConfig(
      clockEdge = RISING,
      resetKind = ASYNC,
      resetActiveLevel = HIGH
    )
  ).generateVerilog(new DisplayCPhyInterface)
  
  println("✓ Basic C-PHY RX example generated")
}

/**
 * Example: Complete C-PHY System with Test Infrastructure
 * 
 * This example shows a complete C-PHY system with built-in
 * test pattern generation and error checking.
 */
object CPhyCompleteSystemExample extends App {
  
  class CPhyTestBench extends Component {
    val io = new Bundle {
      // External connections
      val refClock = in Bool()
      val enable = in Bool()
      val reset = in Bool()
      
      // Test control
      val testMode = in Bool()
      val patternSelect = in UInt(4 bits)
      val loopbackEnable = in Bool()
      
      // External C-PHY connections (for board-to-board testing)
      val cphyTxOut = Vec(master(CPhyTrio()), 2)
      val cphyRxIn = Vec(slave(CPhyTrio()), 2)
      
      // Status and monitoring
      val systemReady = out Bool()
      val testPassed = out Bool()
      val errorCount = out UInt(32 bits)
      val throughput = out UInt(32 bits)
    }
    
    // System configuration
    val systemConfig = CPhySystemConfig(
      dataWidth = 32,
      trioCount = 2,
      symbolRate = 2 GHz,
      useUltraScale = true,
      enableLoopback = false
    )
    
    // Test pattern generator
    val patternGen = CPhyTestPatternGenerator(systemConfig)
    patternGen.io.enable := io.enable && io.testMode
    patternGen.io.patternSelect := io.patternSelect
    
    // C-PHY system
    val cphySystem = CPhySystem(systemConfig)
    cphySystem.io.refClock := io.refClock
    cphySystem.io.enable := io.enable
    cphySystem.io.reset := io.reset
    
    // Data source selection (test pattern or external)
    val dataSource = Stream(Bits(32 bits))
    when(io.testMode) {
      dataSource <> patternGen.io.dataOut
    } otherwise {
      dataSource.valid := False
      dataSource.payload := 0
    }
    cphySystem.io.txData <> dataSource
    
    // Data checker
    val dataChecker = CPhyDataChecker(systemConfig)
    dataChecker.io.dataIn <> cphySystem.io.rxData
    dataChecker.io.expectedPattern := io.patternSelect
    dataChecker.io.enable := io.enable && io.testMode
    
    // External connections with loopback option
    for(i <- 0 until 2) {
      when(io.loopbackEnable) {
        // Internal loopback for testing
        cphySystem.io.trios(i) <> cphySystem.io.trios(i)
      } otherwise {
        // External connections
        io.cphyTxOut(i) <> cphySystem.io.trios(i)
        cphySystem.io.trios(i) <> io.cphyRxIn(i)
      }
    }
    
    // Performance monitoring
    val performanceMonitor = new Area {
      val dataCounter = Reg(UInt(32 bits)) init(0)
      val timeCounter = Reg(UInt(32 bits)) init(0)
      val throughputCalc = Reg(UInt(32 bits)) init(0)
      
      timeCounter := timeCounter + 1
      
      when(cphySystem.io.rxData.fire) {
        dataCounter := dataCounter + 1
      }
      
      // Calculate throughput every 1M cycles
      when(timeCounter(19 downto 0) === 0) {
        throughputCalc := dataCounter
        dataCounter := 0
      }
      
      io.throughput := throughputCalc
    }
    
    // Status outputs
    io.systemReady := cphySystem.io.systemReady
    io.testPassed := dataChecker.io.errorCount === 0 && dataChecker.io.totalCount > 100
    io.errorCount := dataChecker.io.errorCount
  }
  
  // Generate Verilog with timing constraints
  SpinalConfig(
    targetDirectory = "rtl/cphy_examples",
    defaultConfigForClockDomains = ClockDomainConfig(
      clockEdge = RISING,
      resetKind = ASYNC,
      resetActiveLevel = HIGH
    ),
    defaultClockDomainFrequency = FixedFrequency(100 MHz)
  ).generateVerilog(new CPhyTestBench)
  
  println("✓ Complete C-PHY system example generated")
}

/**
 * Example: C-PHY with AXI4-Stream Interface
 * 
 * This example shows how to integrate C-PHY with AXI4-Stream
 * for easy integration with existing IP cores.
 */
object CPhyAxi4StreamExample extends App {
  
  class CPhyAxi4StreamBridge extends Component {
    val io = new Bundle {
      // AXI4-Stream interfaces
      val s_axis = slave(Stream(Bits(32 bits)))
      val m_axis = master(Stream(Bits(32 bits)))
      
      // C-PHY physical interface
      val cphyTrios = Vec(CPhyTrio(), 2)
      
      // Control and status
      val refClock = in Bool()
      val enable = in Bool()
      val reset = in Bool()
      val ready = out Bool()
    }
    
    // C-PHY system
    val cphySystem = CPhySystem(CPhySystemConfig(
      dataWidth = 32,
      trioCount = 2,
      useUltraScale = true
    ))
    
    // Connect AXI4-Stream to C-PHY
    cphySystem.io.txData <> io.s_axis
    io.m_axis <> cphySystem.io.rxData
    
    // Connect physical interface
    for(i <- 0 until 2) {
      io.cphyTrios(i) <> cphySystem.io.trios(i)
    }
    
    // Control connections
    cphySystem.io.refClock := io.refClock
    cphySystem.io.enable := io.enable
    cphySystem.io.reset := io.reset
    io.ready := cphySystem.io.systemReady
  }
  
  // Generate Verilog
  SpinalConfig(
    targetDirectory = "rtl/cphy_examples"
  ).generateVerilog(new CPhyAxi4StreamBridge)
  
  println("✓ C-PHY AXI4-Stream bridge example generated")
}
