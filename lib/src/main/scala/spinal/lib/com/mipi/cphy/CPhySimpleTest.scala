package spinal.lib.com.mipi.cphy

import spinal.core._
import spinal.lib._

/**
 * Simple test to verify C-PHY code compilation
 */
object CPhySimpleTest extends App {
  
  // Test basic C-PHY encoder
  class SimpleEncoder extends Component {
    val io = new Bundle {
      val dataIn = slave(Stream(Bits(16 bits)))
      val symbolOut = master(Stream(Vec(Bits(3 bits), 1)))
    }
    
    val encoder = CPhyEncoder(CPhyConfig(dataWidth = 16, trioCount = 1))
    encoder.io.dataIn <> io.dataIn
    io.symbolOut <> encoder.io.symbolOut
  }
  
  // Test basic C-PHY decoder
  class SimpleDecoder extends Component {
    val io = new Bundle {
      val symbolIn = slave(Stream(Vec(Bits(3 bits), 1)))
      val dataOut = master(Stream(Bits(16 bits)))
    }
    
    val decoder = CPhyDecoder(CPhyConfig(dataWidth = 16, trioCount = 1))
    decoder.io.symbolIn <> io.symbolIn
    io.dataOut <> decoder.io.dataOut
  }
  
  // Test basic C-PHY system
  class SimpleCPhySystem extends Component {
    val io = new Bundle {
      val txData = slave(Stream(Bits(16 bits)))
      val rxData = master(Stream(Bits(16 bits)))
      val refClock = in Bool()
      val enable = in Bool()
      val reset = in Bool()
      val ready = out Bool()
    }
    
    // Use non-UltraScale version for simplicity
    val config = CPhySystemConfig(
      dataWidth = 16,
      trioCount = 1,
      useUltraScale = false,
      enableLoopback = true
    )
    
    val cphySystem = CPhySystem(config)
    cphySystem.io.txData <> io.txData
    cphySystem.io.rxData <> io.rxData
    cphySystem.io.refClock := io.refClock
    cphySystem.io.enable := io.enable
    cphySystem.io.reset := io.reset
    io.ready := cphySystem.io.systemReady
  }
  
  // Generate Verilog to test compilation
  try {
    SpinalConfig(
      targetDirectory = "rtl/test",
      defaultConfigForClockDomains = ClockDomainConfig(
        clockEdge = RISING,
        resetKind = ASYNC,
        resetActiveLevel = HIGH
      )
    ).generateVerilog(new SimpleEncoder)
    
    SpinalConfig(
      targetDirectory = "rtl/test",
      defaultConfigForClockDomains = ClockDomainConfig(
        clockEdge = RISING,
        resetKind = ASYNC,
        resetActiveLevel = HIGH
      )
    ).generateVerilog(new SimpleDecoder)
    
    SpinalConfig(
      targetDirectory = "rtl/test",
      defaultConfigForClockDomains = ClockDomainConfig(
        clockEdge = RISING,
        resetKind = ASYNC,
        resetActiveLevel = HIGH
      )
    ).generateVerilog(new SimpleCPhySystem)
    
    println("✅ C-PHY code compilation test passed!")
    
  } catch {
    case e: Exception =>
      println(s"❌ C-PHY code compilation test failed: ${e.getMessage}")
      e.printStackTrace()
  }
}
