package spinal.lib.com.mipi.cphy

import spinal.core._
import spinal.lib._

/**
 * Complete C-PHY System Configuration
 */
case class CPhySystemConfig(
  dataWidth: Int = 32,
  trioCount: Int = 2,
  symbolRate: HertzNumber = 2 GHz,
  useUltraScale: Boolean = true,
  enableLoopback: Boolean = false
) {
  require(dataWidth > 0 && isPow2(dataWidth), "Data width must be power of 2")
  require(trioCount > 0 && trioCount <= 4, "Trio count must be between 1 and 4")
}

/**
 * C-PHY System Interface
 */
case class CPhySystemInterface(config: CPhySystemConfig) extends Bundle with IMasterSlave {
  val tx = new Bundle {
    val data = slave(Stream(Bits(config.dataWidth bits)))
    val enable = in Bool()
    val ready = out Bool()
  }
  
  val rx = new Bundle {
    val data = master(Stream(Bits(config.dataWidth bits)))
    val enable = in Bool()
    val locked = out Bool()
  }
  
  val trios = Vec(CPhyTrio(), config.trioCount)
  val control = CPhyControlInterface()
  
  override def asMaster(): Unit = {
    master(tx.data)
    out(tx.enable)
    in(tx.ready)

    slave(rx.data)
    out(rx.enable)
    in(rx.locked)

    for(i <- 0 until config.trioCount) {
      master(trios(i))
    }
    master(control)
  }
}

/**
 * Complete C-PHY Transceiver System
 */
case class CPhySystem(config: CPhySystemConfig) extends Component {
  val io = new Bundle {
    val txData = slave(Stream(Bits(config.dataWidth bits)))
    val rxData = master(Stream(Bits(config.dataWidth bits)))
    val trios = Vec(CPhyTrio(), config.trioCount)

    // Clock and control
    val refClock = in Bool()
    val enable = in Bool()
    val reset = in Bool()

    // Status
    val txReady = out Bool()
    val rxLocked = out Bool()
    val systemReady = out Bool()
  }

  // Create components
  val transmitter = if(config.useUltraScale) {
    CPhyTxUltraScale(CPhyTxConfig(
      dataWidth = config.dataWidth,
      trioCount = config.trioCount,
      symbolRate = config.symbolRate
    ))
  } else {
    CPhyTxWithClock(CPhyTxConfig(
      dataWidth = config.dataWidth,
      trioCount = config.trioCount,
      symbolRate = config.symbolRate
    ))
  }

  val receiver = if(config.useUltraScale) {
    CPhyRxUltraScale(CPhyRxConfig(
      dataWidth = config.dataWidth,
      trioCount = config.trioCount,
      symbolRate = config.symbolRate
    ))
  } else {
    CPhyRxWithAGC(CPhyRxConfig(
      dataWidth = config.dataWidth,
      trioCount = config.trioCount,
      symbolRate = config.symbolRate
    ))
  }

  // Connections
  val connections = new Area {
    // Connect transmitter
    transmitter.io.dataIn <> io.txData
    transmitter.io.refClock := io.refClock
    transmitter.io.enable := io.enable
    transmitter.io.reset := io.reset
    io.txReady := transmitter.io.ready

    // Connect receiver
    receiver.io.dataOut <> io.rxData
    receiver.io.refClock := io.refClock
    receiver.io.enable := io.enable
    receiver.io.reset := io.reset
    io.rxLocked := receiver.io.locked

    // Trio connections
    if(config.enableLoopback) {
      // Internal loopback for testing
      for(i <- 0 until config.trioCount) {
        receiver.io.triosIn(i) <> transmitter.io.triosOut(i)
        io.trios(i) <> transmitter.io.triosOut(i)
      }
    } else {
      // External connections
      for(i <- 0 until config.trioCount) {
        io.trios(i) <> transmitter.io.triosOut(i)
        receiver.io.triosIn(i) <> io.trios(i)
      }
    }

    // System status
    io.systemReady := io.txReady && io.rxLocked
  }
}

/**
 * C-PHY Test Pattern Generator
 */
case class CPhyTestPatternGenerator(config: CPhySystemConfig) extends Component {
  val io = new Bundle {
    val dataOut = master(Stream(Bits(config.dataWidth bits)))
    val enable = in Bool()
    val patternSelect = in UInt(4 bits)
  }
  
  val patternGenerator = new Area {
    val counter = Reg(UInt(32 bits)) init(0)
    val patternData = Reg(Bits(config.dataWidth bits)) init(0)
    
    when(io.enable) {
      counter := counter + 1
      
      switch(io.patternSelect) {
        is(0) {
          // Incrementing pattern
          patternData := counter.resize(config.dataWidth).asBits
        }
        is(1) {
          // Alternating pattern
          patternData := counter(0) ? B((1 << config.dataWidth) - 1, config.dataWidth bits) | B(0, config.dataWidth bits)
        }
        is(2) {
          // Walking ones
          val walkingOnes = UInt(log2Up(config.dataWidth) bits)
          walkingOnes := counter.resize(log2Up(config.dataWidth))
          patternData := (U(1) << walkingOnes).resize(config.dataWidth).asBits
        }
        is(3) {
          // PRBS pattern (simplified)
          val prbs = Reg(Bits(config.dataWidth bits)) init(1)
          prbs := Cat(prbs(config.dataWidth-2 downto 0), prbs(config.dataWidth-1) ^ prbs(config.dataWidth-2))
          patternData := prbs
        }
        default {
          patternData := B(0, config.dataWidth bits)
        }
      }
    } otherwise {
      counter := 0
      patternData := 0
    }
  }
  
  io.dataOut.valid := io.enable
  io.dataOut.payload := patternGenerator.patternData
}

/**
 * C-PHY Data Checker
 */
case class CPhyDataChecker(config: CPhySystemConfig) extends Component {
  val io = new Bundle {
    val dataIn = slave(Stream(Bits(config.dataWidth bits)))
    val expectedPattern = in UInt(4 bits)
    val enable = in Bool()
    
    // Status outputs
    val errorCount = out UInt(32 bits)
    val totalCount = out UInt(32 bits)
    val errorRate = out UInt(16 bits)
  }
  
  val checker = new Area {
    val expectedCounter = Reg(UInt(32 bits)) init(0)
    val receivedCounter = Reg(UInt(32 bits)) init(0)
    val errorCounter = Reg(UInt(32 bits)) init(0)
    
    val expectedData = Bits(config.dataWidth bits)
    
    // Generate expected pattern
    switch(io.expectedPattern) {
      is(0) {
        expectedData := expectedCounter.resize(config.dataWidth).asBits
      }
      is(1) {
        expectedData := expectedCounter(0) ? B((1 << config.dataWidth) - 1, config.dataWidth bits) | B(0, config.dataWidth bits)
      }
      is(2) {
        val walkingOnes = expectedCounter.resize(log2Up(config.dataWidth))
        expectedData := (U(1) << walkingOnes).resize(config.dataWidth).asBits
      }
      default {
        expectedData := B(0, config.dataWidth bits)
      }
    }
    
    when(io.dataIn.fire && io.enable) {
      receivedCounter := receivedCounter + 1
      expectedCounter := expectedCounter + 1
      
      when(io.dataIn.payload =/= expectedData) {
        errorCounter := errorCounter + 1
      }
    }
    
    // Calculate error rate (errors per 1000 packets)
    val errorRate = (errorCounter * 1000) / (receivedCounter + 1)
    
    io.errorCount := errorCounter
    io.totalCount := receivedCounter
    io.errorRate := errorRate.resize(16)
  }
  
  io.dataIn.ready := True  // Always ready to receive
}

/**
 * Complete C-PHY Test System
 */
case class CPhyTestSystem(config: CPhySystemConfig) extends Component {
  val io = new Bundle {
    val refClock = in Bool()
    val enable = in Bool()
    val reset = in Bool()
    val patternSelect = in UInt(4 bits)
    
    // External trio connections (for board-level testing)
    val triosOut = Vec(master(CPhyTrio()), config.trioCount)
    val triosIn = Vec(slave(CPhyTrio()), config.trioCount)
    
    // Status and monitoring
    val systemReady = out Bool()
    val errorCount = out UInt(32 bits)
    val errorRate = out UInt(16 bits)
  }
  
  // Test pattern generator
  val patternGen = CPhyTestPatternGenerator(config)
  patternGen.io.enable := io.enable
  patternGen.io.patternSelect := io.patternSelect
  
  // C-PHY system
  val cphySystem = CPhySystem(config.copy(enableLoopback = false))
  cphySystem.io.txData <> patternGen.io.dataOut
  cphySystem.io.refClock := io.refClock
  cphySystem.io.enable := io.enable
  cphySystem.io.reset := io.reset
  
  // Data checker
  val dataChecker = CPhyDataChecker(config)
  dataChecker.io.dataIn <> cphySystem.io.rxData
  dataChecker.io.expectedPattern := io.patternSelect
  dataChecker.io.enable := io.enable
  
  // External connections
  for(i <- 0 until config.trioCount) {
    io.triosOut(i) <> cphySystem.io.trios(i)
    cphySystem.io.trios(i) <> io.triosIn(i)
  }
  
  // Status outputs
  io.systemReady := cphySystem.io.systemReady
  io.errorCount := dataChecker.io.errorCount
  io.errorRate := dataChecker.io.errorRate
}
