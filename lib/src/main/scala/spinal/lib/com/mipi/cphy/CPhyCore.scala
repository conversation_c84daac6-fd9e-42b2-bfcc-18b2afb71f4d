package spinal.lib.com.mipi.cphy

import spinal.core._
import spinal.lib._

/**
 * MIPI C-PHY Configuration Parameters
 * 
 * @param dataWidth     Width of parallel data interface (typically 8, 16, or 32 bits)
 * @param trioCount     Number of C-PHY trios (lanes)
 * @param symbolRate    Symbol rate in symbols per second (up to 2.5 Gsps)
 * @param enableTx      Enable transmitter functionality
 * @param enableRx      Enable receiver functionality
 */
case class CPhyConfig(
  dataWidth: Int = 16,
  trioCount: Int = 1,
  symbolRate: HertzNumber = 1 GHz,
  enableTx: Boolean = true,
  enableRx: Boolean = true
) {
  require(dataWidth > 0 && isPow2(dataWidth), "Data width must be power of 2")
  require(trioCount > 0 && trioCount <= 4, "Trio count must be between 1 and 4")
  require(symbolRate.toBigDecimal <= 2.5e9, "Symbol rate cannot exceed 2.5 Gsps")
}

/**
 * C-PHY Trio Interface - represents the 3-wire differential signaling
 * Each trio consists of three single-ended signals (A, B, C)
 */
case class CPhyTrio() extends Bundle with IMasterSlave {
  val a = Bool()  // Wire A
  val b = Bool()  // Wire B  
  val c = Bool()  // Wire C
  
  override def asMaster(): Unit = {
    out(a, b, c)
  }
}

/**
 * C-PHY Symbol - represents a 3-phase encoded symbol
 * C-PHY uses 7 valid symbols out of 27 possible 3-phase states
 */
object CPhySymbol {
  // Valid C-PHY symbols (3-phase encoding)
  // Each symbol represents 2.28 bits of data (16/7 ratio)
  val SYMBOL_0 = B"000"  // +1, 0, -1
  val SYMBOL_1 = B"001"  // +1, -1, 0  
  val SYMBOL_2 = B"010"  // 0, +1, -1
  val SYMBOL_3 = B"011"  // -1, +1, 0
  val SYMBOL_4 = B"100"  // -1, 0, +1
  val SYMBOL_5 = B"101"  // 0, -1, +1
  val SYMBOL_6 = B"110"  // Flip symbol
  
  def isValidSymbol(symbol: Bits): Bool = {
    symbol === SYMBOL_0 || symbol === SYMBOL_1 || symbol === SYMBOL_2 ||
    symbol === SYMBOL_3 || symbol === SYMBOL_4 || symbol === SYMBOL_5 ||
    symbol === SYMBOL_6
  }
}

/**
 * C-PHY Data Interface - parallel data interface
 */
case class CPhyDataInterface(config: CPhyConfig) extends Bundle with IMasterSlave {
  val data = Bits(config.dataWidth bits)
  val valid = Bool()
  val ready = Bool()
  
  override def asMaster(): Unit = {
    out(data, valid)
    in(ready)
  }
}

/**
 * C-PHY Control and Status Interface
 */
case class CPhyControlInterface() extends Bundle with IMasterSlave {
  val enable = Bool()
  val reset = Bool()
  val calibrate = Bool()
  val powerDown = Bool()
  
  // Status signals
  val locked = Bool()
  val calibrationDone = Bool()
  val errorDetected = Bool()
  
  override def asMaster(): Unit = {
    out(enable, reset, calibrate, powerDown)
    in(locked, calibrationDone, errorDetected)
  }
}

/**
 * Main C-PHY Core Interface
 */
case class CPhyInterface(config: CPhyConfig) extends Bundle with IMasterSlave {
  val ctrl = CPhyControlInterface()
  val data = CPhyDataInterface(config)
  val trios = Vec(CPhyTrio(), config.trioCount)
  
  override def asMaster(): Unit = {
    master(ctrl)
    master(data)
    master(trios)
  }
}

/**
 * C-PHY 3-Phase Encoder
 * Converts parallel data to C-PHY symbols using 16/7 encoding
 */
case class CPhyEncoder(config: CPhyConfig) extends Component {
  val io = new Bundle {
    val dataIn = slave(Stream(Bits(config.dataWidth bits)))
    val symbolOut = master(Stream(Vec(Bits(3 bits), config.trioCount)))
  }
  
  // 16-to-7 symbol mapping table
  val symbolMap = Vec(
    CPhySymbol.SYMBOL_0, CPhySymbol.SYMBOL_1, CPhySymbol.SYMBOL_2,
    CPhySymbol.SYMBOL_3, CPhySymbol.SYMBOL_4, CPhySymbol.SYMBOL_5,
    CPhySymbol.SYMBOL_6
  )
  
  // Data buffer for 16/7 conversion
  val dataBuffer = Reg(Bits((config.dataWidth + 16) bits)) init(0)
  val bufferLevel = Reg(UInt(log2Up(config.dataWidth + 16) bits)) init(0)
  
  // Symbol generation logic
  val symbolReady = bufferLevel >= 16
  val symbols = Vec(Bits(3 bits), config.trioCount)
  
  for(i <- 0 until config.trioCount) {
    val symbolIndex = dataBuffer(i*3+2 downto i*3).asUInt
    symbols(i) := symbolMap(symbolIndex.resize(3))
  }
  
  io.dataIn.ready := bufferLevel <= config.dataWidth
  io.symbolOut.valid := symbolReady
  io.symbolOut.payload := symbols
  
  when(io.dataIn.fire) {
    dataBuffer := (dataBuffer |<< config.dataWidth) | io.dataIn.payload.resize(config.dataWidth + 16)
    bufferLevel := bufferLevel + config.dataWidth
  }
  
  when(io.symbolOut.fire) {
    dataBuffer := dataBuffer |>> (config.trioCount * 3)
    bufferLevel := bufferLevel - (config.trioCount * 3)
  }
}

/**
 * C-PHY 3-Phase Decoder  
 * Converts C-PHY symbols back to parallel data using 7/16 decoding
 */
case class CPhyDecoder(config: CPhyConfig) extends Component {
  val io = new Bundle {
    val symbolIn = slave(Stream(Vec(Bits(3 bits), config.trioCount)))
    val dataOut = master(Stream(Bits(config.dataWidth bits)))
  }
  
  // Symbol-to-data mapping (reverse of encoder)
  val dataMap = Vec.fill(8)(B"0000")
  dataMap(0) := B"0000"  // SYMBOL_0
  dataMap(1) := B"0001"  // SYMBOL_1
  dataMap(2) := B"0010"  // SYMBOL_2
  dataMap(3) := B"0011"  // SYMBOL_3
  dataMap(4) := B"0100"  // SYMBOL_4
  dataMap(5) := B"0101"  // SYMBOL_5
  dataMap(6) := B"0110"  // SYMBOL_6
  dataMap(7) := B"0111"  // Invalid
  
  // Data reconstruction buffer
  val dataBuffer = Reg(Bits((config.dataWidth + 16) bits)) init(0)
  val bufferLevel = Reg(UInt(log2Up(config.dataWidth + 16) bits)) init(0)
  
  // Symbol decoding logic
  val decodedData = Bits(config.trioCount * 4 bits)
  for(i <- 0 until config.trioCount) {
    val symbolIndex = io.symbolIn.payload(i).asUInt
    decodedData(i*4+3 downto i*4) := dataMap(symbolIndex)
  }
  
  val dataReady = bufferLevel >= config.dataWidth
  
  io.symbolIn.ready := bufferLevel <= (config.dataWidth + 16 - config.trioCount * 4)
  io.dataOut.valid := dataReady
  io.dataOut.payload := dataBuffer(config.dataWidth-1 downto 0)
  
  when(io.symbolIn.fire) {
    dataBuffer := (dataBuffer |<< (config.trioCount * 4)) | decodedData.resize(config.dataWidth + 16)
    bufferLevel := bufferLevel + (config.trioCount * 4)
  }
  
  when(io.dataOut.fire) {
    dataBuffer := dataBuffer |>> config.dataWidth
    bufferLevel := bufferLevel - config.dataWidth
  }
}

/**
 * C-PHY Clock Recovery Unit
 * Recovers embedded clock from C-PHY trio signals
 */
case class CPhyClockRecovery(config: CPhyConfig) extends Component {
  val io = new Bundle {
    val trioIn = slave(CPhyTrio())
    val recoveredClock = out Bool()
    val locked = out Bool()
  }
  
  // Phase detector for clock recovery
  val phaseDetector = new Area {
    val prevState = RegNext(Vec(io.trioIn.a, io.trioIn.b, io.trioIn.c))
    val currentState = Vec(io.trioIn.a, io.trioIn.b, io.trioIn.c)
    
    // Detect transitions for clock recovery
    val transition = prevState.zip(currentState).map { case (prev, curr) => prev ^ curr }.reduce(_ || _)
    val transitionCount = Reg(UInt(8 bits)) init(0)
    
    when(transition) {
      transitionCount := transitionCount + 1
    }
  }
  
  // Simple clock recovery (in real implementation, would use PLL/DLL)
  val clockCounter = Reg(UInt(8 bits)) init(0)
  clockCounter := clockCounter + 1
  
  io.recoveredClock := clockCounter.msb
  io.locked := phaseDetector.transitionCount > 10  // Simple lock detection
}
