package spinal.lib.com.mipi.cphy

import spinal.core._
import spinal.lib._
import spinal.lib.blackbox.xilinx.ultrascale._

/**
 * C-PHY Transmitter using Xilinx UltraScale primitives
 * Optimized for high-speed operation on UltraScale devices
 */
case class CPhyTxUltraScale(config: CPhyTxConfig) extends Component {
  val io = new Bundle {
    val dataIn = slave(Stream(Bits(config.dataWidth bits)))
    val triosOut = Vec(master(CPhyTrio()), config.trioCount)
    
    // Clock inputs
    val refClock = in Bool()  // Reference clock (e.g., 100 MHz)
    val enable = in Bool()
    val reset = in Bool()
    
    // Status outputs
    val locked = out Bool()
    val ready = out Bool()
    val calibrationDone = out Bool()
  }
  
  // Clock generation using MMCME4
  val clockGen = new Area {
    val mmcm = MMCME4_BASE(
      CLKIN1_PERIOD = 10.0,      // 100 MHz input
      CLKFBOUT_MULT_F = 10.0,    // 1 GHz VCO
      CLKOUT0_DIVIDE_F = 1.0,    // 1 GHz high-speed clock
      CLKOUT1_DIVIDE = 4,        // 250 MHz word clock
      CLKOUT2_DIVIDE = 16        // 62.5 MHz system clock
    )
    
    mmcm.CLKIN1 := io.refClock
    mmcm.CLKFBIN := mmcm.CLKFBOUT
    mmcm.RST := io.reset
    
    val highSpeedClock = mmcm.CLKOUT0    // 1 GHz for OSERDES
    val wordClock = mmcm.CLKOUT1         // 250 MHz for parallel data
    val systemClock = mmcm.CLKOUT2       // 62.5 MHz for control logic
    
    io.locked := mmcm.LOCKED
  }
  
  // Clock domain definitions
  val highSpeedDomain = ClockDomain(clockGen.highSpeedClock, io.reset)
  val wordClockDomain = ClockDomain(clockGen.wordClock, io.reset)
  val systemClockDomain = ClockDomain(clockGen.systemClock, io.reset)
  
  // Data encoding in system clock domain
  val encoderArea = new ClockingArea(systemClockDomain) {
    val encoder = CPhyEncoder(CPhyConfig(
      dataWidth = config.dataWidth,
      trioCount = config.trioCount
    ))
    
    // Clock domain crossing from input to system domain
    val inputCC = StreamCCByToggle(
      input = io.dataIn,
      inputClock = ClockDomain.current,
      outputClock = systemClockDomain
    )
    encoder.io.dataIn <> inputCC
  }
  
  // Symbol serialization for each trio
  val serializerAreas = for(trioIndex <- 0 until config.trioCount) yield new Area {
    // Symbol buffer in word clock domain
    val symbolBuffer = new ClockingArea(wordClockDomain) {
      val buffer = StreamFifo(Bits(3 bits), 32)
      
      // Clock crossing from system to word domain
      val symbolStream = Stream(Bits(3 bits))
      symbolStream.valid := encoderArea.encoder.io.symbolOut.valid
      symbolStream.payload := encoderArea.encoder.io.symbolOut.payload(trioIndex)

      val symbolCC = StreamCCByToggle(
        input = symbolStream,
        inputClock = systemClockDomain,
        outputClock = wordClockDomain
      )
      buffer.io.push <> symbolCC
    }
    
    // OSERDES for each wire in the trio (A, B, C)
    val trioSerdes = for(wireIndex <- 0 until 3) yield new Area {
      val oserdes = OSERDES3(
        DATA_RATE_OQ = "DDR",
        DATA_WIDTH = 8,
        SERDES_MODE = "MASTER"
      )
      
      // Connect clocks
      oserdes.CLK := clockGen.highSpeedClock
      oserdes.CLKDIV := clockGen.wordClock
      oserdes.RST := io.reset
      
      // Symbol to trio wire mapping
      val symbolToWire = new ClockingArea(wordClockDomain) {
        val currentSymbol = Reg(Bits(3 bits)) init(0)
        val symbolValid = Reg(Bool()) init(False)
        
        when(symbolBuffer.buffer.io.pop.fire) {
          currentSymbol := symbolBuffer.buffer.io.pop.payload
          symbolValid := True
        }
        
        // 3-phase symbol to trio state mapping
        val wireStates = Vec(
          // Symbol 0: A=+1, B=0, C=-1
          Vec(True, False, False),
          // Symbol 1: A=+1, B=-1, C=0
          Vec(True, False, True),
          // Symbol 2: A=0, B=+1, C=-1
          Vec(False, True, False),
          // Symbol 3: A=-1, B=+1, C=0
          Vec(False, True, True),
          // Symbol 4: A=-1, B=0, C=+1
          Vec(False, False, True),
          // Symbol 5: A=0, B=-1, C=+1
          Vec(True, False, True),
          // Symbol 6: Flip
          Vec(False, False, False),
          // Symbol 7: Invalid
          Vec(False, False, False)
        )
        
        val symbolIndex = currentSymbol.asUInt.resize(3)
        val wireState = wireStates(symbolIndex)(wireIndex)
        
        // Generate 8-bit pattern for DDR operation
        val pattern = Reg(Bits(8 bits)) init(0)
        when(symbolValid) {
          pattern := wireState ? B"11110000" | B"00001111"
        }
        
        // Connect to OSERDES data inputs
        for(i <- 0 until 8) {
          oserdes.D(i+1) := pattern(i)
        }
      }
      
      symbolBuffer.buffer.io.pop.ready := True  // Always ready for now
    }
    
    // Output delay elements for timing adjustment
    val outputDelays = for(wireIndex <- 0 until 3) yield new Area {
      val odelay = ODELAYE3(
        DELAY_TYPE = "VARIABLE",
        DELAY_VALUE = 0,
        REFCLK_FREQUENCY = 300.0
      )
      
      odelay.CLK := clockGen.systemClock
      odelay.RST := io.reset
      odelay.ODATAIN := trioSerdes(wireIndex).oserdes.OQ
      odelay.CNTVALUEIN := U(0, 9 bits)  // Default delay
      
      // Connect to trio output
      wireIndex match {
        case 0 => io.triosOut(trioIndex).a := odelay.DATAOUT
        case 1 => io.triosOut(trioIndex).b := odelay.DATAOUT
        case 2 => io.triosOut(trioIndex).c := odelay.DATAOUT
      }
    }
  }
  
  // Status and control logic
  val controlArea = new ClockingArea(systemClockDomain) {
    val calibrationCounter = Reg(UInt(16 bits)) init(0)
    val calibrationDone = Reg(Bool()) init(False)
    
    when(io.enable && clockGen.mmcm.LOCKED) {
      calibrationCounter := calibrationCounter + 1
      when(calibrationCounter > 10000) {
        calibrationDone := True
      }
    } otherwise {
      calibrationCounter := 0
      calibrationDone := False
    }
    
    io.calibrationDone := calibrationDone
    io.ready := calibrationDone && encoderArea.encoder.io.dataIn.ready
  }
}

/**
 * C-PHY Receiver using Xilinx UltraScale primitives
 * Optimized for high-speed operation on UltraScale devices
 */
case class CPhyRxUltraScale(config: CPhyRxConfig) extends Component {
  val io = new Bundle {
    val triosIn = Vec(slave(CPhyTrio()), config.trioCount)
    val dataOut = master(Stream(Bits(config.dataWidth bits)))
    
    // Clock inputs
    val refClock = in Bool()  // Reference clock for IDELAYCTRL
    val enable = in Bool()
    val reset = in Bool()
    
    // Status outputs
    val locked = out Bool()
    val dataValid = out Bool()
    val errorDetected = out Bool()
  }
  
  // IDELAYCTRL for input delay control
  val idelayCtrl = IDELAYCTRL()
  idelayCtrl.REFCLK := io.refClock
  idelayCtrl.RST := io.reset
  
  // Clock recovery and generation (simplified)
  val clockRecovery = new Area {
    // In a real implementation, this would be a complex CDR circuit
    // For now, we'll use a simple approach
    val recoveredClock = Reg(Bool()) init(False)
    val clockCounter = Reg(UInt(8 bits)) init(0)
    
    clockCounter := clockCounter + 1
    when(clockCounter.msb) {
      recoveredClock := !recoveredClock
    }
    
    val wordClock = BUFGCE_DIV(recoveredClock, divide = 4)
    val systemClock = BUFGCE_DIV(recoveredClock, divide = 16)
  }
  
  // Clock domains
  val highSpeedDomain = ClockDomain(clockRecovery.recoveredClock, io.reset)
  val wordClockDomain = ClockDomain(clockRecovery.wordClock, io.reset)
  val systemClockDomain = ClockDomain(clockRecovery.systemClock, io.reset)
  
  // Deserializer for each trio
  val deserializerAreas = for(trioIndex <- 0 until config.trioCount) yield new Area {
    // Input delay and ISERDES for each wire
    val trioDeserdes = for(wireIndex <- 0 until 3) yield new Area {
      // Input delay for timing adjustment
      val idelay = IDELAYE3(
        DELAY_TYPE = "VARIABLE",
        DELAY_VALUE = 0,
        REFCLK_FREQUENCY = 300.0
      )
      
      idelay.CLK := clockRecovery.systemClock
      idelay.RST := io.reset
      idelay.EN_VTC := idelayCtrl.RDY
      
      // Connect input
      wireIndex match {
        case 0 => idelay.IDATAIN := io.triosIn(trioIndex).a
        case 1 => idelay.IDATAIN := io.triosIn(trioIndex).b
        case 2 => idelay.IDATAIN := io.triosIn(trioIndex).c
      }
      
      // ISERDES for deserialization
      val iserdes = ISERDES3(
        DATA_RATE = "DDR",
        DATA_WIDTH = 8,
        INTERFACE_TYPE = "MEMORY"
      )
      
      iserdes.CLK := clockRecovery.recoveredClock
      iserdes.CLKDIV := clockRecovery.wordClock
      iserdes.RST := io.reset
      iserdes.D := idelay.DATAOUT
    }
    
    // Symbol reconstruction in word clock domain
    val symbolReconstruction = new ClockingArea(wordClockDomain) {
      val wireData = Vec(Bits(8 bits), 3)
      for(i <- 0 until 3) {
        wireData(i) := Cat(
          trioDeserdes(i).iserdes.Q8,
          trioDeserdes(i).iserdes.Q7,
          trioDeserdes(i).iserdes.Q6,
          trioDeserdes(i).iserdes.Q5,
          trioDeserdes(i).iserdes.Q4,
          trioDeserdes(i).iserdes.Q3,
          trioDeserdes(i).iserdes.Q2,
          trioDeserdes(i).iserdes.Q1
        )
      }
      
      // Simple symbol detection (would be more complex in real implementation)
      val detectedSymbol = Reg(Bits(3 bits)) init(0)
      val symbolValid = Reg(Bool()) init(False)
      
      // Detect symbol based on wire patterns
      val wireStates = wireData.map(_(7 downto 4).orR)  // Check high nibble
      val symbolIndex = Cat(wireStates).asUInt
      
      detectedSymbol := symbolIndex.resize(3).asBits
      symbolValid := True  // Simplified validation
      
      val symbolStream = Stream(Bits(3 bits))
      symbolStream.valid := symbolValid
      symbolStream.payload := detectedSymbol
    }
  }
  
  // Symbol decoding in system clock domain
  val decoderArea = new ClockingArea(systemClockDomain) {
    val decoder = CPhyDecoder(CPhyConfig(
      dataWidth = config.dataWidth,
      trioCount = config.trioCount
    ))
    
    // Collect symbols from all trios
    val symbolCollector = Stream(Vec(Bits(3 bits), config.trioCount))
    for(i <- 0 until config.trioCount) {
      // Clock crossing from word to system domain
      val symbolCC = StreamCCByToggle(
        input = deserializerAreas(i).symbolReconstruction.symbolStream,
        inputClock = wordClockDomain,
        outputClock = systemClockDomain
      )
      symbolCollector.payload(i) := symbolCC.payload
    }
    symbolCollector.valid := True  // Simplified
    
    decoder.io.symbolIn <> symbolCollector
  }
  
  // Output clock crossing
  val outputArea = new Area {
    val outputCC = StreamCCByToggle(
      input = decoderArea.decoder.io.dataOut,
      inputClock = systemClockDomain,
      outputClock = ClockDomain.current
    )
    io.dataOut <> outputCC
  }
  
  // Status signals
  io.locked := idelayCtrl.RDY
  io.dataValid := io.dataOut.valid
  io.errorDetected := False  // Simplified
}
