package spinal.lib.blackbox.xilinx.ultrascale

import spinal.core._
import spinal.lib._

/**
 * Xilinx UltraScale BUFGCE_DIV - Global Clock Buffer with Clock Enable and Divide
 * Used for clock distribution with programmable divide ratio
 */
case class BUFGCE_DIV(
  BUFGCE_DIVIDE: Int = 1,
  IS_CE_INVERTED: Boolean = false,
  IS_CLR_INVERTED: Boolean = false,
  IS_I_INVERTED: Boolean = false,
  SIM_DEVICE: String = "ULTRASCALE"
) extends BlackBox {
  
  addGeneric("BUFGCE_DIVIDE", BUFGCE_DIVIDE)
  addGeneric("IS_CE_INVERTED", if(IS_CE_INVERTED) "TRUE" else "FALSE")
  addGeneric("IS_CLR_INVERTED", if(IS_CLR_INVERTED) "TRUE" else "FALSE")
  addGeneric("IS_I_INVERTED", if(IS_I_INVERTED) "TRUE" else "FALSE")
  addGeneric("SIM_DEVICE", SIM_DEVICE)
  
  val I = in Bool()    // Clock input
  val CE = in Bool()   // Clock enable
  val CLR = in Bool()  // Clear
  val O = out Bool()   // Clock output
  
  // Default connections
  CE := True
  CLR := False
}

object BUFGCE_DIV {
  def apply(clock: Bool, enable: Bool = True, clear: Bool = False, divide: Int = 1): Bool = {
    val bufg = BUFGCE_DIV(BUFGCE_DIVIDE = divide)
    bufg.setCompositeName(clock, s"BUFGCE_DIV_${divide}")
    bufg.I := clock
    bufg.CE := enable
    bufg.CLR := clear
    bufg.O
  }
}

/**
 * Xilinx UltraScale MMCME4_BASE - Mixed-Mode Clock Manager
 * Used for clock generation and management in C-PHY implementation
 */
case class MMCME4_BASE(
  BANDWIDTH: String = "OPTIMIZED",
  CLKFBOUT_MULT_F: Double = 5.0,
  CLKFBOUT_PHASE: Double = 0.0,
  CLKIN1_PERIOD: Double = 10.0,
  CLKOUT0_DIVIDE_F: Double = 1.0,
  CLKOUT0_DUTY_CYCLE: Double = 0.5,
  CLKOUT0_PHASE: Double = 0.0,
  CLKOUT1_DIVIDE: Int = 1,
  CLKOUT1_DUTY_CYCLE: Double = 0.5,
  CLKOUT1_PHASE: Double = 0.0,
  CLKOUT2_DIVIDE: Int = 1,
  CLKOUT2_DUTY_CYCLE: Double = 0.5,
  CLKOUT2_PHASE: Double = 0.0,
  CLKOUT3_DIVIDE: Int = 1,
  CLKOUT3_DUTY_CYCLE: Double = 0.5,
  CLKOUT3_PHASE: Double = 0.0,
  CLKOUT4_DIVIDE: Int = 1,
  CLKOUT4_DUTY_CYCLE: Double = 0.5,
  CLKOUT4_PHASE: Double = 0.0,
  CLKOUT5_DIVIDE: Int = 1,
  CLKOUT5_DUTY_CYCLE: Double = 0.5,
  CLKOUT5_PHASE: Double = 0.0,
  CLKOUT6_DIVIDE: Int = 1,
  CLKOUT6_DUTY_CYCLE: Double = 0.5,
  CLKOUT6_PHASE: Double = 0.0,
  DIVCLK_DIVIDE: Int = 1,
  IS_CLKFBIN_INVERTED: Boolean = false,
  IS_CLKIN1_INVERTED: Boolean = false,
  IS_PWRDWN_INVERTED: Boolean = false,
  IS_RST_INVERTED: Boolean = false,
  REF_JITTER1: Double = 0.0,
  STARTUP_WAIT: String = "FALSE"
) extends BlackBox {
  
  addGeneric("BANDWIDTH", BANDWIDTH)
  addGeneric("CLKFBOUT_MULT_F", CLKFBOUT_MULT_F)
  addGeneric("CLKFBOUT_PHASE", CLKFBOUT_PHASE)
  addGeneric("CLKIN1_PERIOD", CLKIN1_PERIOD)
  addGeneric("CLKOUT0_DIVIDE_F", CLKOUT0_DIVIDE_F)
  addGeneric("CLKOUT0_DUTY_CYCLE", CLKOUT0_DUTY_CYCLE)
  addGeneric("CLKOUT0_PHASE", CLKOUT0_PHASE)
  addGeneric("CLKOUT1_DIVIDE", CLKOUT1_DIVIDE)
  addGeneric("CLKOUT1_DUTY_CYCLE", CLKOUT1_DUTY_CYCLE)
  addGeneric("CLKOUT1_PHASE", CLKOUT1_PHASE)
  addGeneric("CLKOUT2_DIVIDE", CLKOUT2_DIVIDE)
  addGeneric("CLKOUT2_DUTY_CYCLE", CLKOUT2_DUTY_CYCLE)
  addGeneric("CLKOUT2_PHASE", CLKOUT2_PHASE)
  addGeneric("CLKOUT3_DIVIDE", CLKOUT3_DIVIDE)
  addGeneric("CLKOUT3_DUTY_CYCLE", CLKOUT3_DUTY_CYCLE)
  addGeneric("CLKOUT3_PHASE", CLKOUT3_PHASE)
  addGeneric("CLKOUT4_DIVIDE", CLKOUT4_DIVIDE)
  addGeneric("CLKOUT4_DUTY_CYCLE", CLKOUT4_DUTY_CYCLE)
  addGeneric("CLKOUT4_PHASE", CLKOUT4_PHASE)
  addGeneric("CLKOUT5_DIVIDE", CLKOUT5_DIVIDE)
  addGeneric("CLKOUT5_DUTY_CYCLE", CLKOUT5_DUTY_CYCLE)
  addGeneric("CLKOUT5_PHASE", CLKOUT5_PHASE)
  addGeneric("CLKOUT6_DIVIDE", CLKOUT6_DIVIDE)
  addGeneric("CLKOUT6_DUTY_CYCLE", CLKOUT6_DUTY_CYCLE)
  addGeneric("CLKOUT6_PHASE", CLKOUT6_PHASE)
  addGeneric("DIVCLK_DIVIDE", DIVCLK_DIVIDE)
  addGeneric("IS_CLKFBIN_INVERTED", if(IS_CLKFBIN_INVERTED) "TRUE" else "FALSE")
  addGeneric("IS_CLKIN1_INVERTED", if(IS_CLKIN1_INVERTED) "TRUE" else "FALSE")
  addGeneric("IS_PWRDWN_INVERTED", if(IS_PWRDWN_INVERTED) "TRUE" else "FALSE")
  addGeneric("IS_RST_INVERTED", if(IS_RST_INVERTED) "TRUE" else "FALSE")
  addGeneric("REF_JITTER1", REF_JITTER1)
  addGeneric("STARTUP_WAIT", STARTUP_WAIT)
  
  // Input clocks
  val CLKIN1 = in Bool()
  val CLKFBIN = in Bool()
  
  // Output clocks
  val CLKOUT0 = out Bool()
  val CLKOUT0B = out Bool()
  val CLKOUT1 = out Bool()
  val CLKOUT1B = out Bool()
  val CLKOUT2 = out Bool()
  val CLKOUT2B = out Bool()
  val CLKOUT3 = out Bool()
  val CLKOUT3B = out Bool()
  val CLKOUT4 = out Bool()
  val CLKOUT5 = out Bool()
  val CLKOUT6 = out Bool()
  
  // Feedback clock
  val CLKFBOUT = out Bool()
  val CLKFBOUTB = out Bool()
  
  // Control
  val RST = in Bool()
  val PWRDWN = in Bool()
  
  // Status
  val LOCKED = out Bool()
  
  // Default connections
  RST := False
  PWRDWN := False
}

/**
 * Xilinx UltraScale PLLE4_BASE - Phase-Locked Loop
 * Used for clock generation with lower jitter than MMCM
 */
case class PLLE4_BASE(
  BANDWIDTH: String = "OPTIMIZED",
  CLKFBOUT_MULT: Int = 5,
  CLKFBOUT_PHASE: Double = 0.0,
  CLKIN1_PERIOD: Double = 10.0,
  CLKOUT0_DIVIDE: Int = 1,
  CLKOUT0_DUTY_CYCLE: Double = 0.5,
  CLKOUT0_PHASE: Double = 0.0,
  CLKOUT1_DIVIDE: Int = 1,
  CLKOUT1_DUTY_CYCLE: Double = 0.5,
  CLKOUT1_PHASE: Double = 0.0,
  DIVCLK_DIVIDE: Int = 1,
  IS_CLKFBIN_INVERTED: Boolean = false,
  IS_CLKIN1_INVERTED: Boolean = false,
  IS_PWRDWN_INVERTED: Boolean = false,
  IS_RST_INVERTED: Boolean = false,
  REF_JITTER1: Double = 0.0,
  STARTUP_WAIT: String = "FALSE"
) extends BlackBox {
  
  addGeneric("BANDWIDTH", BANDWIDTH)
  addGeneric("CLKFBOUT_MULT", CLKFBOUT_MULT)
  addGeneric("CLKFBOUT_PHASE", CLKFBOUT_PHASE)
  addGeneric("CLKIN1_PERIOD", CLKIN1_PERIOD)
  addGeneric("CLKOUT0_DIVIDE", CLKOUT0_DIVIDE)
  addGeneric("CLKOUT0_DUTY_CYCLE", CLKOUT0_DUTY_CYCLE)
  addGeneric("CLKOUT0_PHASE", CLKOUT0_PHASE)
  addGeneric("CLKOUT1_DIVIDE", CLKOUT1_DIVIDE)
  addGeneric("CLKOUT1_DUTY_CYCLE", CLKOUT1_DUTY_CYCLE)
  addGeneric("CLKOUT1_PHASE", CLKOUT1_PHASE)
  addGeneric("DIVCLK_DIVIDE", DIVCLK_DIVIDE)
  addGeneric("IS_CLKFBIN_INVERTED", if(IS_CLKFBIN_INVERTED) "TRUE" else "FALSE")
  addGeneric("IS_CLKIN1_INVERTED", if(IS_CLKIN1_INVERTED) "TRUE" else "FALSE")
  addGeneric("IS_PWRDWN_INVERTED", if(IS_PWRDWN_INVERTED) "TRUE" else "FALSE")
  addGeneric("IS_RST_INVERTED", if(IS_RST_INVERTED) "TRUE" else "FALSE")
  addGeneric("REF_JITTER1", REF_JITTER1)
  addGeneric("STARTUP_WAIT", STARTUP_WAIT)
  
  // Input clocks
  val CLKIN1 = in Bool()
  val CLKFBIN = in Bool()
  
  // Output clocks
  val CLKOUT0 = out Bool()
  val CLKOUT0B = out Bool()
  val CLKOUT1 = out Bool()
  val CLKOUT1B = out Bool()
  
  // Feedback clock
  val CLKFBOUT = out Bool()
  val CLKFBOUTB = out Bool()
  
  // Control
  val RST = in Bool()
  val PWRDWN = in Bool()
  
  // Status
  val LOCKED = out Bool()
  
  // Default connections
  RST := False
  PWRDWN := False
}

/**
 * Xilinx UltraScale IDELAYCTRL - Input Delay Control
 * Required for IDELAYE3 operation
 */
case class IDELAYCTRL(
  SIM_DEVICE: String = "ULTRASCALE"
) extends BlackBox {
  
  addGeneric("SIM_DEVICE", SIM_DEVICE)
  
  val REFCLK = in Bool()  // Reference clock (typically 300 MHz)
  val RST = in Bool()     // Reset
  val RDY = out Bool()    // Ready output
  
  // Default simulation behavior
  RDY := True
}

object IDELAYCTRL {
  def apply(refClock: Bool, reset: Bool = False): Bool = {
    val ctrl = IDELAYCTRL()
    ctrl.setCompositeName(refClock, "IDELAYCTRL")
    ctrl.REFCLK := refClock
    ctrl.RST := reset
    ctrl.RDY
  }
}
