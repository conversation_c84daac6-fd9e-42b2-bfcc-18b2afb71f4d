package spinal.lib.blackbox.xilinx.ultrascale

import spinal.core._
import spinal.lib._

/**
 * Xilinx UltraScale ISERDES3 - Input Serial-to-Parallel Converter
 * Used for high-speed data reception in C-PHY implementation
 */
case class ISERDES3(
  DATA_RATE: String = "DDR",
  DATA_WIDTH: Int = 8,
  INTERFACE_TYPE: String = "MEMORY",
  IOBDELAY: String = "NONE",
  NUM_CE: Int = 2,
  SERDES_MODE: String = "MASTER"
) extends BlackBox {
  
  addGeneric("DATA_RATE", DATA_RATE)
  addGeneric("DATA_WIDTH", DATA_WIDTH)
  addGeneric("INTERFACE_TYPE", INTERFACE_TYPE)
  addGeneric("IOBDELAY", IOBDELAY)
  addGeneric("NUM_CE", NUM_CE)
  addGeneric("SERDES_MODE", SERDES_MODE)
  
  // Clock inputs
  val CLK = in Bool()
  val CLKB = in Bool()
  val CLKDIV = in Bool()
  val CLKDIVP = in Bool()
  
  // Data inputs
  val D = in Bool()
  val DDLY = in Bool()
  
  // Control inputs
  val CE1 = in Bool()
  val CE2 = in Bool()
  val RST = in Bool()
  
  // Data outputs (parallel)
  val Q1 = out Bool()
  val Q2 = out Bool()
  val Q3 = out Bool()
  val Q4 = out Bool()
  val Q5 = out Bool()
  val Q6 = out Bool()
  val Q7 = out Bool()
  val Q8 = out Bool()
  
  // Cascade outputs for slave ISERDES
  val SHIFTOUT1 = out Bool()
  val SHIFTOUT2 = out Bool()
  
  // Cascade inputs from master ISERDES
  val SHIFTIN1 = in Bool()
  val SHIFTIN2 = in Bool()
  
  // Bitslip for alignment
  val BITSLIP = in Bool()
  
  // FIFO outputs
  val FIFO_EMPTY = out Bool()
  val FIFO_RDCLK = in Bool()
  val FIFO_RDEN = in Bool()
  val FIFO_WRCLK = in Bool()
  val FIFO_WREN = in Bool()
  
  def Q(i: Int): Bool = i match {
    case 1 => Q1
    case 2 => Q2
    case 3 => Q3
    case 4 => Q4
    case 5 => Q5
    case 6 => Q6
    case 7 => Q7
    case 8 => Q8
  }
  
  // Default connections for unused signals
  CLKB := False
  CLKDIVP := False
  DDLY := False
  CE1 := True
  CE2 := True
  SHIFTIN1 := False
  SHIFTIN2 := False
  BITSLIP := False
  FIFO_RDCLK := False
  FIFO_RDEN := False
  FIFO_WRCLK := False
  FIFO_WREN := False
}

/**
 * Xilinx UltraScale OSERDES3 - Output Parallel-to-Serial Converter
 * Used for high-speed data transmission in C-PHY implementation
 */
case class OSERDES3(
  DATA_RATE_OQ: String = "DDR",
  DATA_RATE_TQ: String = "DDR", 
  DATA_WIDTH: Int = 8,
  SERDES_MODE: String = "MASTER",
  TRISTATE_WIDTH: Int = 1
) extends BlackBox {
  
  addGeneric("DATA_RATE_OQ", DATA_RATE_OQ)
  addGeneric("DATA_RATE_TQ", DATA_RATE_TQ)
  addGeneric("DATA_WIDTH", DATA_WIDTH)
  addGeneric("SERDES_MODE", SERDES_MODE)
  addGeneric("TRISTATE_WIDTH", TRISTATE_WIDTH)
  
  // Clock inputs
  val CLK = in Bool()
  val CLKDIV = in Bool()
  
  // Data inputs (parallel)
  val D1 = in Bool()
  val D2 = in Bool()
  val D3 = in Bool()
  val D4 = in Bool()
  val D5 = in Bool()
  val D6 = in Bool()
  val D7 = in Bool()
  val D8 = in Bool()
  
  // Tristate inputs
  val T1 = in Bool()
  val T2 = in Bool()
  val T3 = in Bool()
  val T4 = in Bool()
  
  // Control inputs
  val RST = in Bool()
  
  // Data outputs (serial)
  val OQ = out Bool()
  val TQ = out Bool()
  
  // Feedback outputs
  val OFB = out Bool()
  val TFB = out Bool()
  
  // Cascade outputs for slave OSERDES
  val SHIFTOUT1 = out Bool()
  val SHIFTOUT2 = out Bool()
  
  // Cascade inputs from master OSERDES
  val SHIFTIN1 = in Bool()
  val SHIFTIN2 = in Bool()
  
  def D(i: Int): Bool = i match {
    case 1 => D1
    case 2 => D2
    case 3 => D3
    case 4 => D4
    case 5 => D5
    case 6 => D6
    case 7 => D7
    case 8 => D8
  }
  
  def T(i: Int): Bool = i match {
    case 1 => T1
    case 2 => T2
    case 3 => T3
    case 4 => T4
  }
  
  // Default connections
  T1 := False
  T2 := False
  T3 := False
  T4 := False
  SHIFTIN1 := False
  SHIFTIN2 := False
}

/**
 * Xilinx UltraScale IDELAYE3 - Input Delay Element
 * Used for fine-tuning input timing in C-PHY receiver
 */
case class IDELAYE3(
  CASCADE: String = "NONE",
  DELAY_FORMAT: String = "TIME",
  DELAY_SRC: String = "IDATAIN",
  DELAY_TYPE: String = "FIXED",
  DELAY_VALUE: Int = 0,
  IS_CLK_INVERTED: Boolean = false,
  IS_RST_INVERTED: Boolean = false,
  REFCLK_FREQUENCY: Double = 300.0,
  SIM_DEVICE: String = "ULTRASCALE",
  UPDATE_MODE: String = "ASYNC"
) extends BlackBox {
  
  addGeneric("CASCADE", CASCADE)
  addGeneric("DELAY_FORMAT", DELAY_FORMAT)
  addGeneric("DELAY_SRC", DELAY_SRC)
  addGeneric("DELAY_TYPE", DELAY_TYPE)
  addGeneric("DELAY_VALUE", DELAY_VALUE)
  addGeneric("IS_CLK_INVERTED", if(IS_CLK_INVERTED) "TRUE" else "FALSE")
  addGeneric("IS_RST_INVERTED", if(IS_RST_INVERTED) "TRUE" else "FALSE")
  addGeneric("REFCLK_FREQUENCY", REFCLK_FREQUENCY)
  addGeneric("SIM_DEVICE", SIM_DEVICE)
  addGeneric("UPDATE_MODE", UPDATE_MODE)
  
  // Control inputs
  val CLK = in Bool()
  val RST = in Bool()
  val LOAD = in Bool()
  val INC = in Bool()
  val EN_VTC = in Bool()
  val CE = in Bool()
  
  // Data inputs
  val IDATAIN = in Bool()
  val DATAIN = in Bool()
  
  // Cascade
  val CASC_RETURN = in Bool()
  val CASC_IN = in Bool()
  val CASC_OUT = out Bool()
  
  // Control value
  val CNTVALUEIN = in UInt(9 bits)
  val CNTVALUEOUT = out UInt(9 bits)
  
  // Output
  val DATAOUT = out Bool()
  
  // Default connections
  LOAD := False
  INC := False
  EN_VTC := True
  CE := False
  DATAIN := False
  CASC_RETURN := False
  CASC_IN := False
  CNTVALUEIN := U(0, 9 bits)
}

/**
 * Xilinx UltraScale ODELAYE3 - Output Delay Element
 * Used for fine-tuning output timing in C-PHY transmitter
 */
case class ODELAYE3(
  CASCADE: String = "NONE",
  DELAY_FORMAT: String = "TIME",
  DELAY_TYPE: String = "FIXED",
  DELAY_VALUE: Int = 0,
  IS_CLK_INVERTED: Boolean = false,
  IS_RST_INVERTED: Boolean = false,
  REFCLK_FREQUENCY: Double = 300.0,
  SIM_DEVICE: String = "ULTRASCALE",
  UPDATE_MODE: String = "ASYNC"
) extends BlackBox {
  
  addGeneric("CASCADE", CASCADE)
  addGeneric("DELAY_FORMAT", DELAY_FORMAT)
  addGeneric("DELAY_TYPE", DELAY_TYPE)
  addGeneric("DELAY_VALUE", DELAY_VALUE)
  addGeneric("IS_CLK_INVERTED", if(IS_CLK_INVERTED) "TRUE" else "FALSE")
  addGeneric("IS_RST_INVERTED", if(IS_RST_INVERTED) "TRUE" else "FALSE")
  addGeneric("REFCLK_FREQUENCY", REFCLK_FREQUENCY)
  addGeneric("SIM_DEVICE", SIM_DEVICE)
  addGeneric("UPDATE_MODE", UPDATE_MODE)
  
  // Control inputs
  val CLK = in Bool()
  val RST = in Bool()
  val LOAD = in Bool()
  val INC = in Bool()
  val EN_VTC = in Bool()
  val CE = in Bool()
  
  // Data input
  val ODATAIN = in Bool()
  
  // Cascade
  val CASC_RETURN = in Bool()
  val CASC_IN = in Bool()
  val CASC_OUT = out Bool()
  
  // Control value
  val CNTVALUEIN = in UInt(9 bits)
  val CNTVALUEOUT = out UInt(9 bits)
  
  // Output
  val DATAOUT = out Bool()
  
  // Default connections
  LOAD := False
  INC := False
  EN_VTC := True
  CE := False
  CASC_RETURN := False
  CASC_IN := False
  CNTVALUEIN := U(0, 9 bits)
}
