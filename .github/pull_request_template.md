<!-- Note: text surrounded by these delimiters will not appear in the PR. -->

<!-- If the PR is related to an issue, please mention it (for instance "Closes
#619"). -->

Closes #

# Context, Motivation & Description

<!-- If the issue has a clear description, it may be enough; else describe the
changes done in your PR here. -->

# Impact on code generation

<!-- Please describe the impact on VHDL/Verilog/SystemVerilog code generation.
-->

# Checklist

- [ ] Unit tests were added
- [ ] API changes are or will be documented:
  - using Scaladoc comments: `/** */`?
  - on [RTD](https://github.com/SpinalHDL/SpinalDoc-RTD)?
  - thanks to a [new tracking issue on RTD](https://github.com/SpinalHDL/SpinalDoc-RTD/issues/new?title=Document%20XXX&body=Do%20not%20merge%20until%20SpinalHDL/SpinalHDL%23XXX%20has%20not%20been%20merged)?
