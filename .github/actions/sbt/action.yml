name: 'Install SBT'
description: 'Install and setup SBT to use Scala'

runs:
  using: "composite"
  steps:

    - name: Set up JDK 11
      uses: actions/setup-java@v3
      with:
        java-version: '11'
        distribution: 'adopt'

    - name: Cache SBT cache
      uses: actions/cache@v3
      with:
        path: |
          ~/.ivy2/cache
          ~/.sbt
        key: ${{ runner.os }}-sbt-cache-${{ hashFiles('project/build.properties') }}
