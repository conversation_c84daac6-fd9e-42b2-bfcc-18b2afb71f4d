name: Run tests

on:
  push:
    branches:
      - dev
  pull_request:
    branches:
      - dev
  workflow_dispatch:
    inputs:
      logLevel:
        description: 'Log level'
        required: true
        default: 'warning'

jobs:
  run-test:
    uses: ./.github/workflows/sbt-tests.yml
    with:
      scala_version: '2.12.18'
      runner_os: 'Linux'
      github_sha: ${{ github.sha }}

  scaladoc:
    needs: run-test
    runs-on: ubuntu-latest
    timeout-minutes: 30
    container:
      image: ghcr.io/spinalhdl/docker:latest
    steps:
    - uses: actions/checkout@v3
    - uses: ./.github/actions/get-compiled
    - run: sbt unidoc
